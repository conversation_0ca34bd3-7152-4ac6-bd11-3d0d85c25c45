package com.ctsi.swoa.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 办文办件转办接收表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTransferSwreceiptDTO对象", description="办文办件转办接收表")
public class BizTransferSwreceiptDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 转办表主键id
     */
    @ApiModelProperty(value = "转办表主键id")
    private Long transferId;

    /**
     * 附件关联id
     */
    @ApiModelProperty(value = "附件关联id")
    private Long enclosureFileId;

    /**
     * 转办子表主键id
     */
    @ApiModelProperty(value = "转办子表主键id")
    private Long subId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 办理状态：0-处理中，1-已完成
     */
    @ApiModelProperty(value = "办理状态：0-处理中，1-已完成")
    private Integer status;

    /**
     * 阅读状态：0-未阅，1-已阅
     */
    @ApiModelProperty(value = "阅读状态：0-未阅，1-已阅")
    private Integer read;

    /**
     * 办理情况反馈
     */
    @ApiModelProperty(value = "办理情况反馈")
    private String feedback;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    private Date feedbackTime;

    /**
     * 签收状态:0-待签收,1-已签收
     */
    @ApiModelProperty(value = "签收状态:0-待签收,1-已签收")
    private Integer receiptStatus;

    /**
     * 是否需要反馈：0-需要，1-不需要
     */
    @ApiModelProperty(value = "是否需要反馈：0-需要，1-不需要")
    private Integer feedbackRequired;

    /**
     * 接收单位id
     */
    @ApiModelProperty(value = "接收单位id")
    private Long receiptUnitId;

    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "接收单位名称")
    private String receiptUnitName;

    /**
     * 接收用户id
     */
    @ApiModelProperty(value = "接收用户id")
    private Long receiptUserId;

    /**
     * 接收用户名称
     */
    @ApiModelProperty(value = "接收用户名称")
    private String receiptUserName;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    private LocalDateTime receiptTime;

    /**
     * 第一次打开时间
     */
    @ApiModelProperty(value = "第一次打开时间")
    private LocalDateTime openTime;

    /**
     * 是否打开：0-未打开，1-已打开
     */
    @ApiModelProperty(value = "是否打开：0-未打开，1-已打开")
    private Integer openFlag;

    @ApiModelProperty(value = "开始日期")
    private String startTimeQuery;

    @ApiModelProperty(value = "结束日期")
    private String endTimeQuery;

}
