.switchbutton {
  text-decoration: none;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  cursor: pointer;
  background: #1a7bc9;
  border: 1px solid #1a7bc9;
  -moz-border-radius: 0px 0px 0px 0px;
  -webkit-border-radius: 0px 0px 0px 0px;
  border-radius: 0px 0px 0px 0px;
}
.switchbutton-inner {
  display: inline-block;
  overflow: hidden;
  position: relative;
  top: -1px;
  left: -1px;
}
.switchbutton-on,
.switchbutton-off,
.switchbutton-handle {
  display: inline-block;
  text-align: center;
  height: 100%;
  float: left;
  font-size: 12px;
  -moz-border-radius: 0px 0px 0px 0px;
  -webkit-border-radius: 0px 0px 0px 0px;
  border-radius: 0px 0px 0px 0px;
}
.switchbutton-on {
  background: #6caef5;
  color: #fff;
}
.switchbutton-off {
  background-color: #fafafa;
  color: #404040;
}
.switchbutton-on,
.switchbutton-reversed .switchbutton-off {
  -moz-border-radius: 0px 0 0 0px;
  -webkit-border-radius: 0px 0 0 0px;
  border-radius: 0px 0 0 0px;
}
.switchbutton-off,
.switchbutton-reversed .switchbutton-on {
  -moz-border-radius: 0 0px 0px 0;
  -webkit-border-radius: 0 0px 0px 0;
  border-radius: 0 0px 0px 0;
}
.switchbutton-handle {
  position: absolute;
  top: 0;
  left: 50%;
  background-color: #fafafa;
  color: #404040;
  border: 1px solid #1a7bc9;
  -moz-box-shadow: 0 0 3px 0 #1a7bc9;
  -webkit-box-shadow: 0 0 3px 0 #1a7bc9;
  box-shadow: 0 0 3px 0 #1a7bc9;
}
.switchbutton-value {
  position: absolute;
  top: 0;
  left: -5000px;
}
.switchbutton-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.switchbutton-disabled,
.switchbutton-readonly {
  cursor: default;
}
