<%@ page language="java" contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@ include file="/WEB-INF/jsp/included/_basic_core.jsp" %>
<html>
<head>
    <%@ include file="/WEB-INF/jsp/included/_meta.jsp" %>
    <link href='${path}/css/bootstrap.min.css' rel="stylesheet">
    <link href='${path}/css/main.css' rel="stylesheet">
    <script type="text/javascript" src="${path}/js/jquery.min.js"></script>
    <title>比翼OA新平台代码生成器</title>
    <style type="text/css">
        .container {
            width: 460px;
        }
        .ipt {
            alignment: left;
            width: 350px;
        }
        #alertInfo {
            width: 300px;
        }
        #isActiviti {
            width: 50px!important;
        }
    </style>
    <script type="text/javascript">
        $(function () {
            $('#mbpBtn').click(function () {
                var param = {
                    tableName: $('#tableName').val(),
                    packageName: $('#packageName').val(),
                    module: $('#module').val(),
                    isActiviti: $('#isActiviti').val()
                };
                $.post('${path}/api/mbp/doGenerateCode', param, function (data) {
                    alert(data);
                });
            });
        });
    </script>
</head>
<body>
<div class="container">
    <div class="content">
        <div class="row">
            <div class="login-form">
                <h2>代码生成器配置</h2>
                <br>
                <form id="mbpForm" autocomplete="off">
                    <fieldset>
                        <div class="clearfix">
                            <label for="tableName">数据库表名：</label>
                            <input class="ipt" type="text" placeholder="数据库表名，如cd_test_user" id="tableName" name="tableName" autofocus="autofocus"
                                   required="required">
                        </div>
                        <div class="clearfix">
                            <label for="packageName">包名（全路径）：</label>
                            <input class="ipt" type="text" placeholder="包路径，如com.ctsi.hndx" id="packageName"
                                   name="packageName" autocomplete="off" required="required"/>
                        </div>
                        <div class="clearfix">
                            <label for="module">maven工程模块名：</label>
                            <input class="ipt" type="text" placeholder="模块名称，如/hndxoa-userorg" id="module" name="module"
                                   autocomplete="off" required="required"/>
                        </div>
                        <div class="clearfix">
                            <label for="isActiviti">是否与工作流关联：</label>
                            <select id="isActiviti" name="isActiviti" class="ipt" required="required">
                                <option value="false" selected>否</option>
                                <option value="true">是</option>
                            </select>
                        </div>
                        <br><br>
                        <input id="mbpBtn" class="btn btn-primary" value="生成" type="button">
                    </fieldset>
                </form>
            </div>
        </div>
    </div>
</div>
<%@ include file="/WEB-INF/jsp/included/_footer.jsp" %>
</body>
</html>