<%@ page language="java" contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>

<!-- 用户自定义样式 -->

<!-- jquery,easyui三方插件js库 -->
<script type="text/javascript" src="${path}/js/jQuery-EasyUI-1.4/jquery.Include.js"></script>
<script type="text/javascript" src="${path}/js/jquery.myajax.js"></script>
<script type="text/javascript" src="${path}/js/jquery.form.js"></script>
<!-- 引用EasyUI的国际化文件,让它显示中文 -->
<script type="text/javascript" src="${path}/js/jQuery-EasyUI-1.4/locale/easyui-lang-zh_CN.js"></script>

<link rel="stylesheet" type="text/css" href="${path}/css/common.css" />
<link rel="stylesheet" type="text/css" href="${path}/css/all.css" />

<script type="text/javascript">
    window._BaseUrl = '${path}';

    //弹出加载层
    function load(msg) {
        $("<div class=\"datagrid-mask\"></div>").css({ display: "block", width: "100%", height: $(window).height() }).appendTo("body");
        $("<div class=\"datagrid-mask-msg\"></div>").html(msg).appendTo("body").css({ display: "block", left: ($(document.body).outerWidth(true) - 190) / 2, top: ($(window).height() - 45) / 2 });
    }
    //取消加载层
    function disLoad() {
        $(".datagrid-mask").remove();
        $(".datagrid-mask-msg").remove();
    }
</script>


