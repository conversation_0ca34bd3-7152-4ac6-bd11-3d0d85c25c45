/* Override some defaults */
html, body {
  background-color: #eee;
}
body {
  padding-top: 40px;
}
.container {
  width: 300px;
}

/* The white background content wrapper */
.container > .content {
  background-color: #fff;
  padding: 20px;
  margin: 0 -20px;
  -webkit-border-radius: 10px 10px 10px 10px;
  -moz-border-radius: 10px 10px 10px 10px;
  border-radius: 10px 10px 10px 10px;
  -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.15);
  -moz-box-shadow: 0 1px 2px rgba(0,0,0,.15);
  box-shadow: 0 1px 2px rgba(0,0,0,.15);
}

.login-form {
  margin-left: 65px;
}

legend {
  margin-right: -50px;
  font-weight: bold;
  color: #404040;
}

input[type="text"], input[type="password"] {
  height: 32px!important;
}

.footer-inner {
  padding: 5%;
  text-align: center;
}

#alertInfo {
  margin-top: 12px;
  width: 183px;
  padding: 2px 15px 2px 10px;
}