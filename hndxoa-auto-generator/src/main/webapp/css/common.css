@charset "utf-8";
/* Download : http://www.codefans.net *?
/* Reset css*/
body,h1,h2,h3,h4,h5,h6,div,hr,p,blockquote,dl,dt,dd,ul,ol,li,pre,form,fieldset,legend,button,input,textarea,th,td{margin:0;padding:0}
body,button,input,select,textarea{font:12px/1.5 tahoma,arial,\5b8b\4f53}
body{_overflow:auto;height:100%;margin:0 auto;}
html{_overflow:hidden;height: 100%;}
h1,h2,h3,h4,h5,h6{font-size:100%}
address,cite,dfn,em,var{font-style:normal}
code,kbd,pre,samp{font-family:courier new,courier,monospace}
small{font-size:12px}
ul,ol,li,dl,dt,dd{list-style:none}
sup{vertical-align:text-top}
sub{vertical-align:text-bottom}
legend{color:#000}
fieldset,img{border:0}
button,input,select,textarea{font:12px/1.5 tahoma,arial,sans-serif; vertical-align:middle;/*cursor:pointer*/}
table{border-spacing:0}
.clear{display:block;float:none;clear:both;overflow:hidden;visibility:hidden;width:0;height:0;background:none;border:0;font-size:0}

/*a*/
a{ text-decoration:none; color:#555;}


/*title*/
h1 { font-size:50px;}
h2 { font-size:40px;}
h3 { font-size:30px;}
h4 { font-size:14px;}
h5 { font-size:12px;}
h6 { font-size:9px;}

td{padding: 0px 0px 0px 0px;margin: 0;}


/*滚动条样式*/
html
{  height:100%;
scrollbar-face-color: #CCC;
    scrollbar-arrow-color:#CCC;
    scrollbar-track-color:#e8e7e7;
    scrollbar-shadow-color:#CCC;}
::-webkit-scrollbar {width: 9px; }
::-webkit-scrollbar-track {  /*-webkit-box-shadow: inset 0 0 2px rgba(0,0,0,0.3);-webkit-border-radius: 4px; border-radius: 4px;*/}
::-webkit-scrollbar-thumb {  background: #ccc; /*-webkit-box-shadow: inset 0 0 2px rgba(0,0,0,0.5);*/}
::-webkit-scrollbar-thumb:active {background: #999; }
::-webkit-scrollbar-thumb:hover {background: #999; }
::-webkit-scrollbar-thumb:window-inactive { background: #999;}

.category{ border: 1px solid #E0DFE0; border-radius: 5px; height: auto; width: auto; margin: 30px; display: inline-block; padding: 10px 70px 10px 10px; color: #0093DF; }
.category-title{ margin: 5px; font-size: 16px; }
.category p{ margin: 5px; font-size: 14px; }
.category-text{ margin-left: 40px; }