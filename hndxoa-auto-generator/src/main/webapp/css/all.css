/* 绿色版ui */

html,
body {
	font-size: 14px !important;
}

#left-box {
	font-family: "黑体", "微软雅黑", "宋体";
}


/*index.jsp --  头部样式*/

.main-top {
	height: 60px;
	width: 100%;
	position: relative;
}

.top-right-box {
	width: auto;
	padding: 0;
	position: absolute;
	right: 0;
	border-radius: 20px;
	text-align: right;
}

.top-right-box a img {
	vertical-align: middle;
}

.top-right-box a {
	display: inline-block;
	color: white;
	height: 66px;
	line-height: 66px;
	font-size: 16px;
	/* border-left: 1px solid #dadada; */
	padding: 0 10px;
}

.top-right-box a:hover {
	background-color: #015ca5;
}

.top-left-box {
	float: left;
	margin-left: 22px;
	color: #fff;
}

.top-center-box {
	position: absolute;
	top: 4px;
	left: 500px;
	right: 500px;
	text-align: center;
}

.top-title {
	font-size: 40px;
	color: white;
}


/*菜单栏*/

.menu_item li {
	height: 30px;
	line-height: 30px;
}

.menu_item li:hover a {
	background-color: #D5F6FF;
}

.menu_item li a {
	display: block;
	color: #5D5959;
	outline: 0;
	text-align: left;
	padding-left: 20px;
}

.total-text {
	font-weight: 500;
}

.red {
	color: red;
	/*  margin: 0 10px;*/
}

* {
	list-style: none;
	outline: none !important;
	padding: 0;
	margin: 0;
}


/*账号管理模块开始*/

.windowTable {
	width: 100%;
}

.windowTable1 {
	width: 100%;
}

.windowTable1 td>.input {
	outline: none;
	border-radius: 3px;
	border: 1px solid #ddd;
	height: 22px;
	width: 200px;
}

.windowTable td>.input {
	outline: none;
	border-radius: 3px;
	border: 1px solid #ddd;
	height: 22px;
	width: 220px;
}

.input {
	outline: none;
	border-radius: 3px;
	border: 1px solid #b0bac1;
	height: 22px;
	line-height: 22px;
	padding: 0 5px;
	width: 150px;
}

.windowTable td>#cc {
	height: 22px;
	width: 160px;
	border: 1px solid #ddd;
}

.windowTable td>input.num {
	width: 77px;
	outline: none;
	border-radius: 3px;
	border: 1px solid #ccc;
	box-shadow: 0px 1px 3px #e9e9e9 inset;
}
.windowTable1 input.address{
	width: 592px;
	border-radius: 3px;
    border: 1px solid #ddd;
    height: 22px;
    padding:0 5px;
}
.label-text {
  width: 120px;
  height: 30px;
  background-color: #e9f5ff;
  text-align: right;
}
.input-text{
	width: 230px;
}
.td {
	border: solid #e4e7ea;
	border-width: 0px 1px 1px 0px;
	padding: 0px 8px;
}

input[type="checkbox"] {
	vertical-align: middle;
}


/*主页面左边导航菜单开始*/

.left-nav-item li {
	height: 40px;
	line-height: 40px;
}

.left-nav-item li .item {
	display: block;
	height: 36px;
	line-height: 36px;
	font-size: 17px;
	background-color: #448ACA;
	color: white;
	margin: 2px 0;
	border-radius: 3px;
	text-align: center;
}

.left-nav-item li :hover {
	background-color: #60a5e4;
}


/*所有页面toolbar*/

.toolbar-box {	
	padding: 0 10px;
}
.toolbar-box div{
	margin: 6px 0;
	line-height:31px;
}
.toolbar-box .easyui-validatebox {
	width: 160px;
	height: 25px;
	border: 1px solid #c4ccdf;
	border-radius: 1px;
}
.toolbar-box>div input:focus{
	border: 1px solid #5295ff;
}
.toolbar-box>#searchBox input:focus{
	border: 1px solid #5295ff;
}
/*个人信息*/

.userInfo-title {
	border-bottom: 2px solid #1987D6;
	margin: 0px 20px;
}

.userInfo-info {
	margin: 20px;
}

.userInfo-info li {
	position: relative;
	height: 40px;
	line-height: 40px;
}

.userInfo-info li label {
	display: inline-block;
	float: left;
	width: 100px;
	font-weight: bold;
}

.userInfo-info li span {
	float: left;
}

.btn {
	height: 24px;
	padding: 0 3px;
	border-radius: 3px;
	line-height: 22px;
	color: white !important;
	font-size: 14px;
	background-color: #55C2E1;
	border: 1px solid #55C2E1;
}


/* 主页左边菜单栏 */

.middle {
	width: 1200px;
	margin: 0px auto;
}

.middle .leftMenu {
	width: 260px;
	height: 1650px;
	background-color: #1c2b36;
	float: left;
}

.middle .leftMenu .topMenu {
	height: 42px;
	background-color: #0065a5;
}

.middle .leftMenu .topMenu img {
	margin: 11px;
	float: left;
}

.middle .leftMenu .topMenu .changeMenu {
	cursor: pointer;
}

.middle .leftMenu .topMenu .banshi {
	padding-left: 23px;
}

.middle .leftMenu .topMenu .menuTitle {
	text-align: center;
	color: #F2F2F2;
	float: left;
	line-height: 42px;
	font-size: 16px;
	width: 120px;
}

.menu_list {
	width: 226px;
	background: #065492;
	ovflow:auto;
}

.menu_list ul li {
	display: block;
	text-align: center;
	width: 100%;
	overflow: hidden;
}
.menu_list ul li>p:hover {
	color:#ffffff;
	/* background:url(../images/icon/xiala-white.png) no-repeat 180px center; */
	 background:#0461ab; 
}
.menu_list ul li .fuMenu {
	color: #d8d8d8;
	font-size: 14px;
	cursor: pointer;
	height: 44px;
	line-height: 44px;
	text-align: left;
	width: 206px;
	float: left;
	padding-left: 9px;
	/* background:url(../images/icon/xiala.png) no-repeat 180px center; */
}

.menu_list ul li .xiala {
	/* display:none; */
	float: right;
	margin-right: 13px;
	margin-top: 15px;
}

.menu_list ul li .div1 {
	width: 100%;
	height: auto;
	float: left;
	background: #023b75;
}

.menu_list ul li .div1 .zcd {
	color: #e4f0f9;
	font-size: 14px;
	height: 40px;
	cursor: pointer;
	line-height: 40px;
	text-align: left;
	width: 250px;
	display: block;
	padding-left: 50px;
	overflow:hidden;
}

.div1 {
	height: 200px;
	display: none;
}

.menu_list ul li .div1 .zcd:hover {
	color: #FFF;
	background-color: #00569a;
	font-weight: bold;
}

.removes {
	color: #ffffff !important;
	border-left: 3px solid #ff6b6b;
	background-color: #e23636;
}


/* 布局 */

.header {
	height: 61px;
	background-color: #006d50;
	overflow: hidden;
}

#left-box {
	width: 226px;
	background-color: #065492;
	border: 0;
	float: left;
	overflow-y: auto;
	overflow-x: hidden;
}

#right-box {
	float: left;
}

.footer {
	width: 100%;
	height: 35px;
	line-height: 35px;
	color: #000000;
	overflow: hidden;
	background: #cdced2; 
	text-align: center;
}

.gncd {
	display:none;
	padding: 8px;
	text-align: center;
	background: #3185db;
	color: #fff;
	font-weight: 700;
	font-size: 18px;
}

.leftmenu-icon {
	float: left;
	margin: 11px 8px 0 17px;
}

.date_short {
	width: 120px;
}

.split_sapn {
	padding: 5px 0;
	margin: 10px;
	border-left: 1px solid #bdc3ca;
}

.remark {
	width: 320px;
	height: 80px;
	border: 1px #DDDDDD solid;
	line-height: 20px;
}

.toolbar-box input {
	margin-right: 6px;
}
textarea{
	border: 1px solid #DDDDDD;
	border-radius: 3px;
}
.docLink{
	color: blue;
	text-decoration: underline;
}
.footer p{
	line-height:29px;
}
.user_photo{
	margin-top:15px;
	text-align:center;
}
.user_img{
	border-radius:50px;
	background-color: #FFFFFF;
}
.welcom_word{
	margin:12px 0 15px 0;
	font-weight:700;
	color:#ffffff;	
}
.search_bth,.clear_btn,.import_bth,.add_btn,.edit_btn,.check_btn,.reviewed_btn,.del_btn,.refresh_btn,.allocate_btn,.export_bth{
	display:inline-block;
	margin:0px;
	padding:0px 10px;
	height:27px;
	line-height: 27px;
	text-align:center;	
	color: #fff;
    border-radius: 1px;
}
.search_bth{
	background: #1a8ee4;  
}
.search_bth:hover{
	background: #209bf7;  
}
.clear_btn,.del_btn{
	background-color: #f34f4f;
}
.clear_btn:hover,.del_btn:hover{
	background-color: #f56b6b;
}
.import_bth{
	background: #009688
}
.import_bth:hover{
	background: #0ead9e
}
.export_bth{
	background: #009688
}
.export_bth:hover{
	background: #0ead9e
}
.add_btn{
	background: #009688
}
.add_btn:hover{
	background: #0ead9e
}
.edit_btn{
	background: #009688
}
.edit_btn:hover{
	background: #0ead9e
}
.check_btn{
	background: #009688
}
.check_btn:hover{
	background: #0ead9e
}
.reviewed_btn{
	background: #009688
}
.reviewed_btn:hover{
	background: #0ead9e
}
.refresh_btn{
	background: #009688;
	}
.refresh_btn:hover{
	background: #0ead9e
	}
.allocate_btn{
	background: #009688;
	}
.allocate_btn:hover{
	background: #0ead9e
	}
.logo{
	margin-top:9px;
	margin-left:31px;
}
.text_sp{
	color:#1a2aa9;
	cursor:pointer;
}
.short-textarea{
	width:212px;
}
.dv-table td{
	text-align:center;
	padding:3px 0!important;
}
.a-more{
	color:#000;
	display:inline-block;
}
.textarea{
	margin:2px 0;
	width:592px;
	padding:0 5px;
	/* text-indent:2em; */
}
.address2{
	width:587px;
	border-radius: 3px;
    border: 1px solid #ddd;
    height: 22px;
    padding:0 5px;
}
.textarea2{
	width:587px;
	margin:2px 0;
	padding:0 5px;
}
.docLink1{display: none;}
#remark{
	margin: 2px 0;
}
.users-manual{
	float: right;
}
.users-manual a{
	color: blue;
	font-size: 18px;
	padding: 10px 50px 0px 50px;
	display: block;
}
