package com.ctsi.mbp.service;

import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.ctsi.mbp.config.MbpConfig;
import com.ctsi.mbp.util.MbpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lizuolang (cpyfbAdmin)
 * @Description 代码生成器 代码生成服务
 * @Date 2022/10/28 15:27
 */
@Slf4j
@Service
public class MbpService {

    @Autowired
    private MbpConfig mbpConfig;

    /**
     * 代码生成器入口
     * @param tableName
     * @param module
     * @param packageName
     * @param isActiviti
     */
    public void generateCode(String tableName, String module, String packageName, boolean isActiviti) {
        /**
         * 数据源配置
         */
        DataSourceConfig dataSourceConfig = new DataSourceConfig
                .Builder(this.mbpConfig.getJdbcUrl(), this.mbpConfig.getUserName(), this.mbpConfig.getPassword())
                .build();
        MbpUtil.doGenerateCode(tableName, module, packageName,
                this.mbpConfig.getMapperXmlbasePath(), this.mbpConfig.getBaseSrcPath(), isActiviti, dataSourceConfig);
    }

}
