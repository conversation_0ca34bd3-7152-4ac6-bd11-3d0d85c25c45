package com.ctsi.mbp.util;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MbpUtil {

    /**
     * 获取系统路径
     */
    private static String projectPath = System.getProperty("user.dir");
    private static String author = System.getProperty("user.name");

    /**
     * 代码生成器
     * @param tableName 数据库表名
     * @param module 模块名：模块名称，执行此main函数之前，请务必确保已经新建了模块
     * @param packageName 包名：包路径
     * @param mapperXmlbasePath mapper.xml文件的路径，基本上不需要改动
     * @param baseSrcPath 代码的路径，基本上不需要改动
     * @param isActiviti 业务是否与工作流相关联，
     *                   true表示与工作流相关的，实体类继承ProcessBusinessBaseEntity，会默认带上标题，是否正文附件等业务
     *                   false表示不与工作流相关的，实体类继承BaseEntity
     */
    public static void doGenerateCode(String tableName, String module, String packageName,
                                      String mapperXmlbasePath, String baseSrcPath, boolean isActiviti,
                                      DataSourceConfig dataSourceConfig) {
        log.info("模块名称：" + module);
        log.info("包名：" + packageName);
        log.info("表名：" + tableName);
        log.info("业务是否与工作流相关联（true表示与工作流相关的）：" + isActiviti);
        AutoGenerator generator = new AutoGenerator(dataSourceConfig);
        //策略配置
        generator.strategy(strategyConfig(tableName, isActiviti).build());
        //自定义模板
        generator.template(templateConfig().build());
        //全局配置
        generator.global(globalConfig(module, baseSrcPath).build());
        //包配置
        generator.packageInfo(packageConfig(module, packageName, mapperXmlbasePath).build());
        //自定义输出配置
        final String[] entityName = new String[1];
        generator.injection(new InjectionConfig.Builder().beforeOutputFile((tableInfo, objectMap) -> {
            entityName[0] = tableInfo.getEntityName();
        }).build());
        //开始生成
        generator.execute(new FreemarkerTemplateEngine());
        produceViewObject(entityName[0], module, packageName, baseSrcPath, isActiviti);
    }

    /**
     * 策略配置
     */
    private static StrategyConfig.Builder strategyConfig(String tableName, boolean isActiviti) {
        StrategyConfig.Builder builder = new StrategyConfig.Builder();
        //enableLombok:开启lombok模型,enableHyphenStyle:开启驼峰转连字符,enableRestStyle:开启生成@RestController控制器
        builder.entityBuilder().enableLombok().controllerBuilder().enableHyphenStyle().enableRestStyle();
        String superEntityCalss = "com.ctsi.hndx.common.ProcessBusinessBaseEntity";
        if (!isActiviti) {
            superEntityCalss = "com.ctsi.hndx.common.BaseEntity";
            builder.entityBuilder().addSuperEntityColumns("id", "update_by", "update_name", "update_time",
                    "create_by", "create_name", "create_time", "department_id","department_name", "company_id","company_name", "tenant_id", "deleted");
        } else {
            builder.entityBuilder().addSuperEntityColumns("id", "update_by", "update_name", "update_time",
                    "create_by", "create_name", "create_time", "department_id","department_name", "company_id","company_name", "tenant_id", "deleted",
                    "title", "bpm_status", "document", "annex", "processs_instance_id");
        }
        // 设置需要生成的表名
        builder.addInclude(tableName)
                .serviceBuilder().superServiceClass("com.ctsi.hndx.common.SysBaseServiceI")
                .superServiceImplClass("com.ctsi.hndx.common.SysBaseServiceImpl")
                .entityBuilder().superClass(superEntityCalss)
                .mapperBuilder().superClass("com.ctsi.hndx.common.MybatisBaseMapper");
        return builder;
    }

    /**
     * 全局配置
     */
    private static GlobalConfig.Builder globalConfig(String module, String baseSrcPath) {
        return new GlobalConfig.Builder()
                .enableSwagger()
                .disableOpenDir()
                .outputDir(projectPath + module + baseSrcPath)
                .author(author);
    }

    /**
     * 模板配置
     */
    private static TemplateConfig.Builder templateConfig() {
        return new TemplateConfig.Builder()
                .entity("templates/entity.java")
                .controller("templates/controller.java")
                .service("templates/service.java");
    }

    /**
     * 包配置
     */
    private static PackageConfig.Builder packageConfig(String module, String packageName, String mapperXmlbasePath) {
        //自定义xml地址
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.mapperXml, new StringBuffer(projectPath)
                .append(module)
                .append(mapperXmlbasePath)
                .append(File.separator)
                .append("mapper")
                .toString());
        return new PackageConfig.Builder()
                .parent(packageName).pathInfo(pathInfo);
    }

    /**
     * 根据表名生成VO对象,用于mybatis-plus代码生成以后再生成
     * @param entityName
     */
    private static void produceViewObject(String entityName, String module, String packageName, String baseSrcPath,
                                   boolean isActiviti) {
        String entityPath = new StringBuffer(projectPath)
                .append(module)
                .append(baseSrcPath)
                .append(File.separator)
                .append(packageName.replace(".", "/"))
                .append("/entity/")
                .toString();

        try {
            File outFile = new File(entityPath + "dto/");
            if (!outFile.exists()) {
                outFile.mkdirs();
            }
            File voFile = new File(outFile, entityName + "DTO.java");
            if (!voFile.exists()) {
                voFile.createNewFile();
            }
            BufferedReader reader = new BufferedReader(
                    new FileReader(entityPath + entityName + ".java"));
            FileWriter fw = new FileWriter(voFile);
            String line = null;
            while ((line = reader.readLine()) != null) {
                // 将实体类中的entity变为Vo
                line = line.replace(entityName, entityName + "DTO");
                // 去掉mybatis-plus注解
                if (line.contains("TableName") || line.contains("TableField") || line.contains("Accessors")) {
                    continue;
                }

                if (line.contains("package " + packageName + ".entity.")) {
                    continue;
                }
                line = line.replace("package " + packageName + ".entity", "package " + packageName + ".entity.dto");
                if (isActiviti) {
                    line = line.replace("BaseEntity", "ProcessBusinessBaseDtoEntity");
                } else {
                    line = line.replace("BaseEntity", "BaseDtoEntity");
                }

                line += "\r\n";
                fw.write(line);
            }
            fw.close();
            reader.close();
        } catch (RuntimeException | IOException e) {
            e.printStackTrace();
        }
    }

}
