package com.ctsi.mbp.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: lizuolang (cpyfbAdmin)
 * @Description 代码生成器参数配置
 * @Date 2022/10/28 15:27
 */
@Data
@Configuration
public class MbpConfig {

    @Value("${spring.datasource.url}")
    private String jdbcUrl;

    @Value("${spring.datasource.driverClassName}")
    private String jdbcDriverClass;

    @Value("${spring.datasource.username}")
    private String userName;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${hndxoa.mbp.baseSrcPath:/src/main/java}")
    private String baseSrcPath;

    @Value("${hndxoa.mbp.mapperXmlbasePath:/src/main/resources}")
    private String mapperXmlbasePath;

}
