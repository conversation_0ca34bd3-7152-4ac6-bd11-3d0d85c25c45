package com.ctsi.mbp.dm;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.mbp
 * @ClassName: NewCodeGenerator
 * @Author: json
 * @Description:
 * @Date: 2022/2/9 9:53
 * @Version: 1.0
 */
public class NewCodeGenerator {

    /**
     * 业务是否与工作流相关联，true表示与工作流相关的，实体类集成在 ProcessBusinessBaseEntity，会默认带上标题，
     * 是否正文附件，等等业务
     * false表示不与工作流相关的，实体类继承自 BaseEntity
     */
    private static boolean isActiviti = false;

    /**
     * 表名称
     */
    private static String tableName = "t_dict_record_org";

    /**
     * 模块名称，执行此main函数之前，请务必确保已经新建了模块
     */
    private static String module = "/hndxoa-system";

    /**
     * 包路径
     */
    private static String parent = "com.ctsi.system";

    /**
     * 代码的路径，基本上不需要改动
     */
    private static String basePath = "/src/main/java";

    /**
     * mapper.xml文件的路径，基本上不需要改动
     */
    private static String mapperXmlbasePath = "/src/main/resources";

    /**
     * 获取系统路径
     */
    private static String projectPath = System.getProperty("user.dir");
    private static String author = System.getProperty("user.name");

    /**
     * 数据源配置
     */
    private static final DataSourceConfig DATA_SOURCE_CONFIG = new DataSourceConfig
            .Builder(
            "jdbc:dm://172.17.144.198:5238/OADB?useUnicode=true&characterEncoding=utf8&useSSL=false&clobAsString=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&&keyWords=commit,sort,path,text,user,type,level",
            "OADB",
            "OAdb@2023.")
            .build();

    /**
     * 策略配置
     */
    private static StrategyConfig.Builder strategyConfig() {
        StrategyConfig.Builder builder = new StrategyConfig.Builder();
        //enableLombok:开启lombok模型,enableHyphenStyle:开启驼峰转连字符,enableRestStyle:开启生成@RestController控制器
        builder.entityBuilder().enableLombok().controllerBuilder().enableHyphenStyle().enableRestStyle();
        String superEntityCalss = "com.ctsi.hndx.common.ProcessBusinessBaseEntity";
        if (!isActiviti) {
            superEntityCalss = "com.ctsi.hndx.common.BaseEntity";
            builder.entityBuilder().addSuperEntityColumns("id", "update_by", "update_name", "update_time",
                    "create_by", "create_name", "create_time", "department_id", "company_id", "tenant_id", "deleted");
        } else {
            builder.entityBuilder().addSuperEntityColumns("id", "update_by", "update_name", "update_time",
                    "create_by", "create_name", "create_time", "department_id", "company_id", "tenant_id", "deleted",
                    "title", "bpm_status", "document", "annex", "processs_instance_id");
        }
        // 设置需要生成的表名
        builder.addInclude(tableName)
                .serviceBuilder().superServiceClass("com.ctsi.hndx.common.SysBaseServiceI").superServiceImplClass("com.ctsi.hndx.common.SysBaseServiceImpl")
                .entityBuilder().superClass(superEntityCalss)
                .mapperBuilder().superClass("com.ctsi.hndx.common.MybatisBaseMapper");
        return builder;
    }

    /**
     * 全局配置
     */
    private static GlobalConfig.Builder globalConfig() {
        return new GlobalConfig.Builder()
                .enableSwagger()
                .disableOpenDir()
                .outputDir(projectPath + module + basePath)
                .author(author);
    }

    /**
     * 模板配置
     */
    private static TemplateConfig.Builder templateConfig() {
        return new TemplateConfig.Builder()
                .entity("templates/entity.java")
                .controller("templates/controller.java")
                .service("templates/service.java");
    }

    /**
     * 包配置
     */
    private static PackageConfig.Builder packageConfig() {
        //自定义xml地址
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.mapperXml, new StringBuffer(projectPath).append(module).append(mapperXmlbasePath).append(File.separator).append("mapper").toString());
        return new PackageConfig.Builder()
                .parent(parent).pathInfo(pathInfo);
    }

    /**
     * 门面
     *
     * @param args
     */
    public static void main(String[] args) {
        AutoGenerator generator = new AutoGenerator(DATA_SOURCE_CONFIG);
        //策略配置
        generator.strategy(strategyConfig().build());
        //自定义模板
        generator.template(templateConfig().build());
        //全局配置
        generator.global(globalConfig().build());
        //包配置
        generator.packageInfo(packageConfig().build());
        //自定义输出配置
        final String[] entityName = new String[1];
        generator.injection(new InjectionConfig.Builder().beforeOutputFile((tableInfo, objectMap) -> {
            entityName[0] = tableInfo.getEntityName();
        }).build());
        //开始生成
        generator.execute(new FreemarkerTemplateEngine());

        produceViewObject(projectPath, entityName[0]);
    }


    /**
     * 根据表名生成VO对象,用于mybatis-plus代码生成以后再生成
     *
     * @param projectPath
     * @param entityName
     */
    private static void produceViewObject(String projectPath, String entityName) {
        String entityPath = new StringBuffer(projectPath).append(module).append(basePath).append(File.separator).append(parent.replace(".", "/")).append("/entity/").toString();

        try {
            File outFile = new File(entityPath + "dto/");
            if (!outFile.exists()) {
                outFile.mkdirs();
            }
            File voFile = new File(outFile, entityName + "DTO.java");
            if (!voFile.exists()) {
                voFile.createNewFile();
            }
            BufferedReader reader = new BufferedReader(
                    new FileReader(entityPath + entityName + ".java"));
            FileWriter fw = new FileWriter(voFile);
            String line = null;
            while ((line = reader.readLine()) != null) {
                // 将实体类中的entity变为Vo
                line = line.replace(entityName, entityName + "DTO");
                // 去掉mybatis-plus注解
                if (line.contains("TableName") || line.contains("TableField") || line.contains("Accessors")) {
                    continue;
                }

                if (line.contains("package " + parent + ".entity.")) {
                    continue;
                }
                line = line.replace("package " + parent + ".entity", "package " + parent + ".entity.dto");
                if (isActiviti) {
                    line = line.replace("BaseEntity", "ProcessBusinessBaseDtoEntity");
                } else {
                    line = line.replace("BaseEntity", "BaseDtoEntity");
                }

                line += "\r\n";
                fw.write(line);
            }
            fw.close();
            reader.close();
        } catch (RuntimeException | IOException e) {
            e.printStackTrace();
        }
    }
}
