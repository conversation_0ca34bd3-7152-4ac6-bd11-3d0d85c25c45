package com.ctsi.mbp;

import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.ctsi.mbp.util.MbpUtil;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.Map;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.mbp
 * @ClassName: NewCodeGenerator
 * @Author: json
 * @Description:
 * @Date: 2022/2/9 9:53
 * @Version: 1.0
 */
public class NewCodeGenerator {

    /**
     * 业务是否与工作流相关联，true表示与工作流相关的，实体类集成在 ProcessBusinessBaseEntity，会默认带上标题，
     * 是否正文附件，等等业务
     * false表示不与工作流相关的，实体类继承自 BaseEntity
     */
    private static boolean isActiviti = false;

    /**
     * 表名称
     */
    private static String tableName = "t_top_org_relation";

    /**
     * 模块名称，执行此main函数之前，请务必确保已经新建了模块
     */
    private static String module = "/hndxoa-userorg";

    /**
     * 包路径
     */
    private static String parent = "com.ctsi.hndx";

    /**
     * 代码的路径，基本上不需要改动
     */
    private static String basePath = "/src/main/java";

    /**
     * mapper.xml文件的路径，基本上不需要改动
     */
    private static String mapperXmlbasePath = "/src/main/resources";

    /**
     * 门面
     *
     * @param args
     */
    public static void main(String[] args) {
        Yaml instanceYml = new Yaml();
        Map<String, Object> map = instanceYml.load(NewCodeGenerator.class.getClassLoader()
                .getResourceAsStream("application.yml"));
        Object springMap = map.get("spring");
        Map<String, Object> datasourceMap = (Map) ((Map) springMap).get("datasource");
        String url = datasourceMap.get("url").toString();
        String username = datasourceMap.get("username").toString();
        String password = datasourceMap.get("password").toString();

        /**
         * 数据源配置
         */
        DataSourceConfig dataSourceConfig = new DataSourceConfig.Builder(url, username, password).build();
        MbpUtil.doGenerateCode(tableName, module, parent, mapperXmlbasePath, basePath, isActiviti,
                dataSourceConfig);
    }

}
