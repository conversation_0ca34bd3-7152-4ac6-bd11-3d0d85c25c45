package com.ctsi.mbp.controller;

import com.ctsi.mbp.dto.CodeGeneratorDto;
import com.ctsi.mbp.service.MbpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: lizuolang (cpyfbAdmin)
 * @Description 代码生成控制器
 * @Date 2022/10/28 15:27
 */
@Slf4j
@RestController
@RequestMapping("/api/mbp/")
public class MbpController {

    @Autowired
    private MbpService mbpService;

    @PostMapping("/doGenerateCode")
    public String doGenerateCode(CodeGeneratorDto param, HttpServletRequest request) {
        String mbpMsg = "代码生成成功";
        try {
            if (null == param.getTableName() || "".equals(param.getTableName())) {
                mbpMsg = "代码生成失败，导致到原因是：数据库表名不能为空";
                request.setAttribute("mbpMsg", mbpMsg);
                return mbpMsg;
            }
            if (null == param.getModule() || "".equals(param.getModule())) {
                mbpMsg = "代码生成失败，导致到原因是：maven工程模块名不能为空";
                request.setAttribute("mbpMsg", mbpMsg);
                return mbpMsg;
            }
            if (null == param.getPackageName() || "".equals(param.getPackageName())) {
                mbpMsg = "代码生成失败，导致到原因是：包名（全路径）不能为空";
                request.setAttribute("mbpMsg", mbpMsg);
                return mbpMsg;
            }
            mbpService.generateCode(param.getTableName(), param.getModule(), param.getPackageName(),
                    param.isActiviti);
            request.setAttribute("mbpMsg", mbpMsg);
            return mbpMsg;
        } catch (Exception e) {
            e.printStackTrace();
            mbpMsg = "代码生成失败，导致到原因是：" + e.getMessage();
            request.setAttribute("mbpMsg", mbpMsg);
            return mbpMsg;
        }
    }

}
