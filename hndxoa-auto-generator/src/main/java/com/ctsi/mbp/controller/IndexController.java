package com.ctsi.mbp.controller;

import com.ctsi.mbp.dto.CodeGeneratorDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * ClassName:indexController
 * PackageName:com.ctsi.mbp.controller
 * Description:
 *
 * @date:2023/3/14 10:30
 * @author:13812536906
 */
@Controller
public class IndexController {
    @GetMapping("/index")
    public String goIndex(HttpServletRequest request) {
        return "index";
    }

    @GetMapping("/")
    public String toIndex(HttpServletRequest request) {
        return goIndex(request);
    }

}
