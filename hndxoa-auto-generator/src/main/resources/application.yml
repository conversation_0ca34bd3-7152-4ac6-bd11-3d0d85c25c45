server:
  #端口号
  port: 9002
  jsp:
    init-parameters:
      development: true

# TRACE < DEBUG < INFO < WARN < ERROR < FATAL
logging:
  level:
    root: info
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger -%msg%n"
  file:
    name: ./log/mbp-dev.log
dataBaseName: SWOA
spring:
  datasource:
    driver-class-name: dm.jdbc.driver.DmDriver
    url: jdbc:dm://134.178.223.130:5238/${dataBaseName}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true&clobAsString=true&compatibleMode=mysql
    username: SWOA
    password: SwOA@20240628.
    type: com.zaxxer.hikari.HikariDataSource

  #jsp模板视图配置
  mvc:
    view:
      prefix: /WEB-INF/jsp/
      suffix: .jsp


