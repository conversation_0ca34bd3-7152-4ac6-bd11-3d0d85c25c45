<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD Ctsi MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/ctsi-mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <context id="myapp" targetRuntime="com.ctsi.biyi.generator.plugins.IntrospectedTableBiyiMyBatis3Impl">

        <!-- 数据库类型（mysql,oracle,sqlserver） -->
        <property name="databaseDialect" value="mysql"/>

        <!-- 数据访问层代码使用mybatis或spring data jpa，可选值为mybatis，sdjpa -->
        <property name="dataAccess" value="mybatis"/>

        <!-- 是否生成ES代码 -->
        <property name="useES" value="false"/>

        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin" />
        <!-- 生成表的实体类 -->
        <plugin type="com.ctsi.biyi.generator.plugins.SerializableBiyiPlugin" />
        <!-- 生成mybatis注解 -->
        <plugin type="com.ctsi.biyi.generator.plugins.MapperAnnotationBiyiPlugin" />
        <!-- 生成example类 -->
        <plugin type="com.ctsi.biyi.generator.plugins.ExampleBiyiPlugin" />
        <!-- 生成注释 -->
        <plugin type="com.ctsi.biyi.generator.plugins.ClassCommentPlugin" />

        <plugin type="com.ctsi.biyi.generator.plugins.ServicePlugin" >
            <!-- 代码包名 -->
            <property name="importPackage" value="com.ctsi.hnedu.system.biz.school"/>
            <!-- 代码包名 -->
            <property name="targetPackage" value="com.ctsi.hnedu.system.biz.school"/>
            <!-- 代码路径 -->
            <property name="targetProject" value="src/main/java"/>
            <!-- 项目名 -->
            <property name="targetWebProject" value="..\myapp3.1web"/>
        </plugin>
        <!-- 生成多表查询代码 -->
        <plugin type="com.ctsi.biyi.generator.plugins.UnitSelectPlugin" >
            <!-- 代码包名 -->
            <property name="importPackage" value="com.ctsi.hnedu.system.biz.school"/>
            <!-- 代码包名 -->
            <property name="targetPackage" value="com.ctsi.hnedu.system.biz.school"/>
            <property name="targetProject" value="src/main/java"/>
            <property name="targetWebProject" value="..\myapp3.1web"/>
        </plugin>

        <!-- 数据库连接配置 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="************************************************************************************************************************************************************************************************"
                        userId="root"
                        password="123456">
        </jdbcConnection>

        <javaTypeResolver >
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.ctsi.hnedu.system.biz.school.domain" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
            <property name="trimStrings" value="true" />
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="com.ctsi.hnedu.system.biz.school.xml" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
        </sqlMapGenerator>

        <javaClientGenerator type="ANNOTATEDMAPPER" targetPackage="com.ctsi.hnedu.system.biz.school.repository" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
        </javaClientGenerator>

        <!-- 生成多个表的代码，配置多个table节点 -->
        <table tableName="t_sys_org_school" domainObjectName="School"  >
            <property name="useActualColumnNames" value="false"/>
        </table>

        <!-- 生成多表关联查询代码，不需要则请删除此节点，可以配置多个 -->
<!--        <view viewName="UserView">-->
<!--            <sql id="User" value="select a.username,b.last_login from cscp_user a left join cscp_user_detail b on a.id=b.user_id where b.user_id=1 order by b.user_id desc"/>-->
<!--        </view>-->

    </context>
</generatorConfiguration>
