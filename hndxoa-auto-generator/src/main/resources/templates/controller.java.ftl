package ${package.Controller};
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import ${package.Entity}.${entity};
import ${package.Entity}.dto.${entity}DTO;
import ${package.Service}.${table.serviceName};
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/${entity?uncap_first}")
@Api(value = "${table.comment!}", tags = "${table.comment!}接口")
public class ${entity}Controller extends BaseController {

    private static final String ENTITY_NAME = "${entity?uncap_first}";

    @Autowired
    private ${table.serviceName} ${entity?uncap_first}Service;



    /**
     *  新增${table.comment!}批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.${entity?uncap_first}.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增${table.comment!}批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.${entity?uncap_first}.add')")
    public ResultVO createBatch(@RequestBody List<${entity}DTO> ${entity?uncap_first}List) {
       Boolean  result = ${entity?uncap_first}Service.insertBatch(${entity?uncap_first}List);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.${entity?uncap_first}.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增${table.comment!}数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.${entity?uncap_first}.add')")
    public ResultVO<${entity}DTO> create(@RequestBody ${entity}DTO ${entity?uncap_first}DTO)  {
        ${entity}DTO result = ${entity?uncap_first}Service.create(${entity?uncap_first}DTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.${entity?uncap_first}.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新${table.comment!}数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.${entity?uncap_first}.update')")
    public ResultVO update(@RequestBody ${entity}DTO ${entity?uncap_first}DTO) {
	    Assert.notNull(${entity?uncap_first}DTO.getId(), "general.IdNotNull");
        int count = ${entity?uncap_first}Service.update(${entity?uncap_first}DTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除${table.comment!}数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.${entity?uncap_first}.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.${entity?uncap_first}.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = ${entity?uncap_first}Service.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        ${entity}DTO ${entity?uncap_first}DTO = ${entity?uncap_first}Service.findOne(id);
        return ResultVO.success(${entity?uncap_first}DTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/query${entity}Page")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<${entity}DTO>> query${entity}Page(${entity}DTO ${entity?uncap_first}DTO, BasePageForm basePageForm) {
        return ResultVO.success(${entity?uncap_first}Service.queryListPage(${entity?uncap_first}DTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/query${entity}")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<${entity}DTO>> query${entity}(${entity}DTO ${entity?uncap_first}DTO) {
       List<${entity}DTO> list = ${entity?uncap_first}Service.queryList(${entity?uncap_first}DTO);
       return ResultVO.success(new ResResult<${entity}DTO>(list));
   }

}
