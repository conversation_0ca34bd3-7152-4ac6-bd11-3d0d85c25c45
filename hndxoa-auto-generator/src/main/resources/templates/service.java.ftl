package ${package.Service};

import ${package.Entity}.dto.${entity}DTO;
import ${package.Entity}.${entity};
import ${superServiceClassPackage};
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * ${table.comment!} 服务类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if kotlin>
interface ${table.serviceName} : ${superServiceClass}<${entity}>
<#else>
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<${entity}DTO> queryListPage(${entity}DTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<${entity}DTO> queryList(${entity}DTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    ${entity}DTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    ${entity}DTO create(${entity}DTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(${entity}DTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existBy${entity}Id
     * @param code
     * @return
     */
    boolean existBy${entity}Id(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<${entity}DTO> dataList);


}
</#if>
