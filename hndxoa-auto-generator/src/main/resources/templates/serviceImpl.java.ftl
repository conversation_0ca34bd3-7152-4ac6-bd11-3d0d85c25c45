package ${package.ServiceImpl};

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import ${package.Entity}.${entity};
import ${package.Entity}.dto.${entity}DTO;
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * ${table.comment!} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
@Service
<#if kotlin>
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
<#else>
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

    @Autowired
    private ${entity}Mapper ${entity?uncap_first}Mapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<${entity}DTO> queryListPage(${entity}DTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<${entity}> queryWrapper = new LambdaQueryWrapper();

        IPage<${entity}> pageData = ${entity?uncap_first}Mapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<${entity}DTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,${entity}DTO.class));

        return new PageResult<${entity}DTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<${entity}DTO> queryList(${entity}DTO entityDTO) {
        LambdaQueryWrapper<${entity}> queryWrapper = new LambdaQueryWrapper();
            List<${entity}> listData = ${entity?uncap_first}Mapper.selectList(queryWrapper);
            List<${entity}DTO> ${entity}DTOList = ListCopyUtil.copy(listData, ${entity}DTO.class);
        return ${entity}DTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public ${entity}DTO findOne(Long id) {
        ${entity}  ${entity?uncap_first} =  ${entity?uncap_first}Mapper.selectById(id);
        return  BeanConvertUtils.copyProperties(${entity?uncap_first},${entity}DTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ${entity}DTO create(${entity}DTO entityDTO) {
       ${entity} ${entity?uncap_first} =  BeanConvertUtils.copyProperties(entityDTO,${entity}.class);
        save(${entity?uncap_first});
        return  BeanConvertUtils.copyProperties(${entity?uncap_first},${entity}DTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(${entity}DTO entity) {
        ${entity} ${entity?uncap_first} = BeanConvertUtils.copyProperties(entity,${entity}.class);
        return ${entity?uncap_first}Mapper.updateById(${entity?uncap_first});
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return ${entity?uncap_first}Mapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param ${entity}Id
     * @return
     */
    @Override
    public boolean existBy${entity}Id(Long ${entity}Id) {
        if (${entity}Id != null) {
            LambdaQueryWrapper<${entity}> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(${entity}::getId, ${entity}Id);
            List<${entity}> result = ${entity?uncap_first}Mapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<${entity}DTO> dataList) {
        List<${entity}> result = ListCopyUtil.copy(dataList, ${entity}.class);
        return saveBatch(result);
    }


}
</#if>
