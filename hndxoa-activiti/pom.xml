<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctsi.hndxoa</groupId>
        <artifactId>hndxoa</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.ctsi.hndxoa</groupId>
    <artifactId>hndxoa-activiti</artifactId>
    <version>${hndxoa.activiti.version}</version>
    <packaging>jar</packaging>
    <description>工作流模块</description>
    <name>hndxoa-activiti</name>

    <properties>
        <imageVersion>1.0.0</imageVersion>
        <imageName>image.docker.ssdc.solutions/ctsi/hndxoa-activiti</imageName>
    </properties>



    <dependencies>

<!--      <dependency>-->
<!--            <groupId>com.github.pagehelper</groupId>-->
<!--            <artifactId>pagehelper</artifactId>-->
<!--            <version>5.1.11</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-base</artifactId>
        </dependency>


        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-cform</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-userorg</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-file-operation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-file-operation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-userorg</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-sms</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-basic</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-rest-api</artifactId>

        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>

        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-system</artifactId>
        </dependency>


    </dependencies>



    <build>

    </build>

</project>
