<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpProcViewtaskRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpProcViewtask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
    <result column="PROC_INST_ID" jdbcType="VARCHAR" property="procInstId" />
    <result column="ACT_DEF_ID" jdbcType="VARCHAR" property="actDefId" />
    <result column="FORM_DEF_ID" jdbcType="VARCHAR" property="formDefId" />
    <result column="FORM_DATA_ID" jdbcType="VARCHAR" property="formDataId" />
    <result column="SEQ_NO" jdbcType="VARCHAR" property="seqNo" />
    <result column="PROC_NAME" jdbcType="VARCHAR" property="procName" />
    <result column="ACT_NAME" jdbcType="VARCHAR" property="actName" />
    <result column="PROC_STATUS" jdbcType="VARCHAR" property="procStatus" />
    <result column="SUBJECT" jdbcType="VARCHAR" property="subject" />
    <result column="CRAFTER_ID" jdbcType="VARCHAR" property="crafterId" />
    <result column="CRAFTER_NAME" jdbcType="VARCHAR" property="crafterName" />
    <result column="CREATE_DATE" jdbcType="VARCHAR" property="createDate" />
    <result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
    <result column="COPY_SEND_DEPT" jdbcType="VARCHAR" property="copySendDept" />
    <result column="COPY_USER_ID" jdbcType="VARCHAR" property="copyUserId" />
    <result column="COPY_USER_NAME" jdbcType="VARCHAR" property="copyUserName" />
    <result column="COPY_OPINION" jdbcType="VARCHAR" property="copyOpinion" />
    <result column="COPY_TIME" jdbcType="VARCHAR" property="copyTime" />
    <result column="COPY_STATUS" jdbcType="VARCHAR" property="copyStatus" />
    <result column="PROC_ORGAN" jdbcType="VARCHAR" property="procOrgan" />
  </resultMap>

</mapper>