<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpProcPermissionsRepository">
  <insert id="saveProcs" parameterType="com.ctsi.common.utils.PageData">
            insert into cscp_proc_permissions (
                            ID,
                            PROCESS_DEFINITION_KEY_,
                            PERMISSIONS_TYPE_,
                            PERMISSIONS_ID_,
                            APP_ID,
                            CREATE_DATE,
                            OBJ_TYPE
                        )
                 values
                        (

                            #{ID},
                            #{PROCESS_DEFINITION_KEY_},
                            #{PERMISSIONS_TYPE_},
                            #{PERMISSIONS_ID_},
                            #{APP_ID},
                            #{CREATE_DATE},
                            #{OBJ_TYPE}
                        )
  </insert>

  <select id="queryPageProcPermissions" parameterType="com.ctsi.activiti.core.vo.PageQuery" resultType="com.ctsi.common.utils.PageData">
          select  t.*
            from  cscp_proc_permissions t
           where  t.PROCESS_DEFINITION_KEY_ = #{PROCESS_DEFINITION_KEY_}
        order by  t.CREATE_DATE dessc

  </select>

    <select id="queryPermissionByRoleId" resultType="com.ctsi.common.utils.PageData">
        select ID,PROCESS_DEFINITION_KEY_,PERMISSIONS_TYPE_,PERMISSIONS_ID_,OBJ_TYPE
          from  cscp_proc_permissions t
          where PERMISSIONS_TYPE_ = '2'
          AND PERMISSIONS_ID_ = #{roleId}
    </select>

    <delete id="deletePermissionByRoleId">
          delete
        from  cscp_proc_permissions
          where PERMISSIONS_TYPE_ = '2'
          AND PERMISSIONS_ID_ = #{roleId}
    </delete>


    <select id="checkUserProcessTypePermissions" parameterType="com.ctsi.common.utils.PageData" resultType="com.ctsi.common.utils.PageData">
                    SELECT cpp.PROCESS_DEFINITION_KEY_ FROM cscp_proc_permissions cpp
                    WHERE cpp.PERMISSIONS_TYPE_ = '0' AND cpp.PERMISSIONS_ID_ = #{userId} AND cpp.PROCESS_DEFINITION_KEY_  = #{typeId} AND cpp.OBJ_TYPE = #{objType}
             union
                    SELECT cpp.PROCESS_DEFINITION_KEY_ FROM cscp_proc_permissions cpp
                    LEFT JOIN cscp_user_org cuo ON cuo.org_id = cpp.PERMISSIONS_ID_
                    WHERE cpp.PERMISSIONS_TYPE_ = '1' AND cuo.user_id = #{userId} AND cpp.PROCESS_DEFINITION_KEY_  = #{typeId} AND cpp.OBJ_TYPE = #{objType}
             union
                    SELECT cpp.PROCESS_DEFINITION_KEY_ FROM cscp_proc_permissions cpp
                    WHERE cpp.PERMISSIONS_TYPE_ = '4' AND cpp.PERMISSIONS_ID_ = #{orgTopId} AND cpp.PROCESS_DEFINITION_KEY_  = #{typeId} AND cpp.OBJ_TYPE = #{objType}
             union
                    SELECT cpp.PROCESS_DEFINITION_KEY_ FROM cscp_proc_permissions cpp
                    LEFT JOIN cscp_user_role cur ON cur.role_id = cpp.PERMISSIONS_ID_
                    WHERE cpp.PERMISSIONS_TYPE_ = '2' AND cpp.PERMISSIONS_ID_ IN (${roleId})  AND cpp.PROCESS_DEFINITION_KEY_  = #{typeId} AND cpp.OBJ_TYPE = #{objType}
             <!--union
                    SELECT cpp.PROCESS_DEFINITION_KEY_ FROM cscp_proc_permissions cpp
                    LEFT JOIN cscp_user_org cuo ON cuo.org_id = cpp.PERMISSIONS_ID_
                    WHERE cpp.PERMISSIONS_TYPE_ = '1' AND cuo.user_id = #{userId}-->
    </select>
</mapper>