<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpOutAuthorizationRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpOutAuthorization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 23 16:19:39 CST 2020.
    -->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="STARTTIME" jdbcType="VARCHAR" property="starttime" />
    <result column="ENDTIME" jdbcType="VARCHAR" property="endtime" />
    <result column="MODULES" jdbcType="VARCHAR" property="modules" />
    <result column="MODULENAME" jdbcType="VARCHAR" property="modulename" />
    <result column="AUTHORIZEDUSERID" jdbcType="VARCHAR" property="authorizeduserid" />
    <result column="AUTHORIZEDNAME" jdbcType="VARCHAR" property="authorizedname" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime" />
    <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="AUTH_TYPE" jdbcType="VARCHAR" property="authType" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="CREATEROCESS" jdbcType="VARCHAR" property="createrocess" />
    <result column="TECHPROCESS" jdbcType="VARCHAR" property="techprocess" />
    <result column="TASKTODO" jdbcType="VARCHAR" property="tasktodo" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ctsi.business.domain.CscpOutAuthorization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 23 16:19:39 CST 2020.
    -->
    <result column="OUTADDRESS" jdbcType="LONGVARCHAR" property="outaddress" />
  </resultMap>

</mapper>