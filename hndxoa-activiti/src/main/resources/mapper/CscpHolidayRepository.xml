<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpHolidayRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpHoliday">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 31 09:25:35 CST 2020.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="START_DATE" jdbcType="VARCHAR" property="startDate" />
    <result column="END_DATE" jdbcType="VARCHAR" property="endDate" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>

  <select id="queryPageOvertimeTaskHistory" parameterType="com.ctsi.activiti.core.vo.PageQuery" resultType="com.ctsi.common.utils.PageData">
    SELECT e.TASKID,h.NAME_,h.ASSIGNEE_,e.LIMITTIME FROM cscp_proc_task_extend e
    LEFT JOIN act_hi_taskinst h
    ON
    e.taskid = h.id_
    WHERE limittime IS NOT NULL AND endtime IS NULL AND limittime &lt; DATE_FORMAT(SYSDATE(), '%Y-%m-%d %H:%I:%S')
    <if test="obj.assignee != null and obj.assignee != '' ">
      AND h.ASSIGNEE_ = #{obj.assignee,jdbcType=VARCHAR}
    </if>
    union all
    SELECT e.TASKID,h.NAME_,h.ASSIGNEE_,e.LIMITTIME FROM cscp_proc_task_extend e
    LEFT JOIN act_hi_taskinst h
    ON
    e.taskid = h.id_
    WHERE limittime IS NOT NULL AND endtime IS NOT NULL AND limittime &lt; endtime
    <if test="obj.assignee != null and obj.assignee != '' ">
      AND h.ASSIGNEE_ = #{obj.assignee,jdbcType=VARCHAR}
    </if>
    ORDER BY limittime
  </select>

  <select id="queryPageOvertimeTask" parameterType="com.ctsi.activiti.core.vo.PageQuery" resultType="com.ctsi.common.utils.PageData">
    SELECT e.TASKID,h.NAME_,h.ASSIGNEE_,e.LIMITTIME FROM cscp_proc_task_extend e
    LEFT JOIN act_hi_taskinst h
    ON
    e.taskid = h.id_
    WHERE limittime IS NOT NULL AND endtime IS NULL AND limittime &lt; DATE_FORMAT(SYSDATE(), '%Y-%m-%d %H:%I:%S')
    <if test="obj.assignee != null and obj.assignee != '' ">
      AND h.ASSIGNEE_ = #{obj.assignee,jdbcType=VARCHAR}
    </if>
    ORDER BY e.limittime
  </select>


</mapper>