<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpGroupUserRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpGroupUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="GROUPID" jdbcType="VARCHAR" property="groupid" />
    <result column="PERSONORORGID" jdbcType="VARCHAR" property="personororgid" />
    <result column="GTYPE" jdbcType="VARCHAR" property="gtype" />
    <result column="DISPLAYORDER" jdbcType="INTEGER" property="displayorder" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="ACCOUNT" jdbcType="VARCHAR" property="account" />
    <result column="DEPTID" jdbcType="VARCHAR" property="deptid" />
    <result column="PARENTCITYCODE" jdbcType="VARCHAR" property="parentcitycode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    ID, GROUPID, PERSONORORGID, GTYPE, DISPLAYORDER, NAME, ACCOUNT, DEPTID, PARENTCITYCODE
  </sql>
  <select id="selectByExample" parameterType="com.ctsi.business.domain.CscpGroupUserExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from CSCP_GROUP_USER
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from CSCP_GROUP_USER
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_GROUP_USER
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ctsi.business.domain.CscpGroupUserExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_GROUP_USER
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ctsi.business.domain.CscpGroupUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_GROUP_USER (ID, GROUPID, PERSONORORGID, 
      GTYPE, DISPLAYORDER, NAME, 
      ACCOUNT, DEPTID, PARENTCITYCODE
      )
    values (#{id,jdbcType=VARCHAR}, #{groupid,jdbcType=VARCHAR}, #{personororgid,jdbcType=VARCHAR}, 
      #{gtype,jdbcType=VARCHAR}, #{displayorder,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{account,jdbcType=VARCHAR}, #{deptid,jdbcType=VARCHAR}, #{parentcitycode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ctsi.business.domain.CscpGroupUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_GROUP_USER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="groupid != null">
        GROUPID,
      </if>
      <if test="personororgid != null">
        PERSONORORGID,
      </if>
      <if test="gtype != null">
        GTYPE,
      </if>
      <if test="displayorder != null">
        DISPLAYORDER,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="account != null">
        ACCOUNT,
      </if>
      <if test="deptid != null">
        DEPTID,
      </if>
      <if test="parentcitycode != null">
        PARENTCITYCODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="groupid != null">
        #{groupid,jdbcType=VARCHAR},
      </if>
      <if test="personororgid != null">
        #{personororgid,jdbcType=VARCHAR},
      </if>
      <if test="gtype != null">
        #{gtype,jdbcType=VARCHAR},
      </if>
      <if test="displayorder != null">
        #{displayorder,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="deptid != null">
        #{deptid,jdbcType=VARCHAR},
      </if>
      <if test="parentcitycode != null">
        #{parentcitycode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ctsi.business.domain.CscpGroupUserExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select count(*) from CSCP_GROUP_USER
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP_USER
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.groupid != null">
        GROUPID = #{record.groupid,jdbcType=VARCHAR},
      </if>
      <if test="record.personororgid != null">
        PERSONORORGID = #{record.personororgid,jdbcType=VARCHAR},
      </if>
      <if test="record.gtype != null">
        GTYPE = #{record.gtype,jdbcType=VARCHAR},
      </if>
      <if test="record.displayorder != null">
        DISPLAYORDER = #{record.displayorder,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        NAME = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null">
        ACCOUNT = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.deptid != null">
        DEPTID = #{record.deptid,jdbcType=VARCHAR},
      </if>
      <if test="record.parentcitycode != null">
        PARENTCITYCODE = #{record.parentcitycode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP_USER
    set ID = #{record.id,jdbcType=VARCHAR},
      GROUPID = #{record.groupid,jdbcType=VARCHAR},
      PERSONORORGID = #{record.personororgid,jdbcType=VARCHAR},
      GTYPE = #{record.gtype,jdbcType=VARCHAR},
      DISPLAYORDER = #{record.displayorder,jdbcType=INTEGER},
      NAME = #{record.name,jdbcType=VARCHAR},
      ACCOUNT = #{record.account,jdbcType=VARCHAR},
      DEPTID = #{record.deptid,jdbcType=VARCHAR},
      PARENTCITYCODE = #{record.parentcitycode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ctsi.business.domain.CscpGroupUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP_USER
    <set>
      <if test="groupid != null">
        GROUPID = #{groupid,jdbcType=VARCHAR},
      </if>
      <if test="personororgid != null">
        PERSONORORGID = #{personororgid,jdbcType=VARCHAR},
      </if>
      <if test="gtype != null">
        GTYPE = #{gtype,jdbcType=VARCHAR},
      </if>
      <if test="displayorder != null">
        DISPLAYORDER = #{displayorder,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        ACCOUNT = #{account,jdbcType=VARCHAR},
      </if>
      <if test="deptid != null">
        DEPTID = #{deptid,jdbcType=VARCHAR},
      </if>
      <if test="parentcitycode != null">
        PARENTCITYCODE = #{parentcitycode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ctsi.business.domain.CscpGroupUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP_USER
    set GROUPID = #{groupid,jdbcType=VARCHAR},
      PERSONORORGID = #{personororgid,jdbcType=VARCHAR},
      GTYPE = #{gtype,jdbcType=VARCHAR},
      DISPLAYORDER = #{displayorder,jdbcType=INTEGER},
      NAME = #{name,jdbcType=VARCHAR},
      ACCOUNT = #{account,jdbcType=VARCHAR},
      DEPTID = #{deptid,jdbcType=VARCHAR},
      PARENTCITYCODE = #{parentcitycode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>