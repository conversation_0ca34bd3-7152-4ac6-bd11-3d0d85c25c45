<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpGroupRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="GROUPNAME" jdbcType="VARCHAR" property="groupname" />
    <result column="GROUPTYPE" jdbcType="VARCHAR" property="grouptype" />
    <result column="CREATERID" jdbcType="VARCHAR" property="createrid" />
    <result column="CREATETIME" jdbcType="VARCHAR" property="createtime" />
    <result column="CTCORPCODE" jdbcType="VARCHAR" property="ctcorpcode" />
    <result column="PARENTID" jdbcType="VARCHAR" property="parentid" />
    <result column="DISPLAYORDER" jdbcType="INTEGER" property="displayorder" />
    <result column="ISADMIN" jdbcType="INTEGER" property="isadmin" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    ID, GROUPNAME, GROUPTYPE, CREATERID, CREATETIME, CTCORPCODE, PARENTID, DISPLAYORDER, 
    ISADMIN
  </sql>
  <select id="selectByExample" parameterType="com.ctsi.business.domain.CscpGroupExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from CSCP_GROUP
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from CSCP_GROUP
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_GROUP
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ctsi.business.domain.CscpGroupExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_GROUP
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ctsi.business.domain.CscpGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_GROUP (ID, GROUPNAME, GROUPTYPE, 
      CREATERID, CREATETIME, CTCORPCODE, 
      PARENTID, DISPLAYORDER, ISADMIN
      )
    values (#{id,jdbcType=VARCHAR}, #{groupname,jdbcType=VARCHAR}, #{grouptype,jdbcType=VARCHAR}, 
      #{createrid,jdbcType=VARCHAR}, #{createtime,jdbcType=VARCHAR}, #{ctcorpcode,jdbcType=VARCHAR}, 
      #{parentid,jdbcType=VARCHAR}, #{displayorder,jdbcType=INTEGER}, #{isadmin,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ctsi.business.domain.CscpGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_GROUP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="groupname != null">
        GROUPNAME,
      </if>
      <if test="grouptype != null">
        GROUPTYPE,
      </if>
      <if test="createrid != null">
        CREATERID,
      </if>
      <if test="createtime != null">
        CREATETIME,
      </if>
      <if test="ctcorpcode != null">
        CTCORPCODE,
      </if>
      <if test="parentid != null">
        PARENTID,
      </if>
      <if test="displayorder != null">
        DISPLAYORDER,
      </if>
      <if test="isadmin != null">
        ISADMIN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="groupname != null">
        #{groupname,jdbcType=VARCHAR},
      </if>
      <if test="grouptype != null">
        #{grouptype,jdbcType=VARCHAR},
      </if>
      <if test="createrid != null">
        #{createrid,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=VARCHAR},
      </if>
      <if test="ctcorpcode != null">
        #{ctcorpcode,jdbcType=VARCHAR},
      </if>
      <if test="parentid != null">
        #{parentid,jdbcType=VARCHAR},
      </if>
      <if test="displayorder != null">
        #{displayorder,jdbcType=INTEGER},
      </if>
      <if test="isadmin != null">
        #{isadmin,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ctsi.business.domain.CscpGroupExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select count(*) from CSCP_GROUP
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.groupname != null">
        GROUPNAME = #{record.groupname,jdbcType=VARCHAR},
      </if>
      <if test="record.grouptype != null">
        GROUPTYPE = #{record.grouptype,jdbcType=VARCHAR},
      </if>
      <if test="record.createrid != null">
        CREATERID = #{record.createrid,jdbcType=VARCHAR},
      </if>
      <if test="record.createtime != null">
        CREATETIME = #{record.createtime,jdbcType=VARCHAR},
      </if>
      <if test="record.ctcorpcode != null">
        CTCORPCODE = #{record.ctcorpcode,jdbcType=VARCHAR},
      </if>
      <if test="record.parentid != null">
        PARENTID = #{record.parentid,jdbcType=VARCHAR},
      </if>
      <if test="record.displayorder != null">
        DISPLAYORDER = #{record.displayorder,jdbcType=INTEGER},
      </if>
      <if test="record.isadmin != null">
        ISADMIN = #{record.isadmin,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP
    set ID = #{record.id,jdbcType=VARCHAR},
      GROUPNAME = #{record.groupname,jdbcType=VARCHAR},
      GROUPTYPE = #{record.grouptype,jdbcType=VARCHAR},
      CREATERID = #{record.createrid,jdbcType=VARCHAR},
      CREATETIME = #{record.createtime,jdbcType=VARCHAR},
      CTCORPCODE = #{record.ctcorpcode,jdbcType=VARCHAR},
      PARENTID = #{record.parentid,jdbcType=VARCHAR},
      DISPLAYORDER = #{record.displayorder,jdbcType=INTEGER},
      ISADMIN = #{record.isadmin,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ctsi.business.domain.CscpGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP
    <set>
      <if test="groupname != null">
        GROUPNAME = #{groupname,jdbcType=VARCHAR},
      </if>
      <if test="grouptype != null">
        GROUPTYPE = #{grouptype,jdbcType=VARCHAR},
      </if>
      <if test="createrid != null">
        CREATERID = #{createrid,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        CREATETIME = #{createtime,jdbcType=VARCHAR},
      </if>
      <if test="ctcorpcode != null">
        CTCORPCODE = #{ctcorpcode,jdbcType=VARCHAR},
      </if>
      <if test="parentid != null">
        PARENTID = #{parentid,jdbcType=VARCHAR},
      </if>
      <if test="displayorder != null">
        DISPLAYORDER = #{displayorder,jdbcType=INTEGER},
      </if>
      <if test="isadmin != null">
        ISADMIN = #{isadmin,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ctsi.business.domain.CscpGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_GROUP
    set GROUPNAME = #{groupname,jdbcType=VARCHAR},
      GROUPTYPE = #{grouptype,jdbcType=VARCHAR},
      CREATERID = #{createrid,jdbcType=VARCHAR},
      CREATETIME = #{createtime,jdbcType=VARCHAR},
      CTCORPCODE = #{ctcorpcode,jdbcType=VARCHAR},
      PARENTID = #{parentid,jdbcType=VARCHAR},
      DISPLAYORDER = #{displayorder,jdbcType=INTEGER},
      ISADMIN = #{isadmin,jdbcType=INTEGER}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>