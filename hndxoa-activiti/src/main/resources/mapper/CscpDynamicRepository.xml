<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.activiti.core.mapper.DynamicMapper">

    <update id="updateDynamic" parameterType="com.ctsi.activiti.core.entity.DynamicEntity">
        update ${er.tableName}
        set bpm_status  = ${er.BpmStatus},
            update_by= ${er.updateBy},
            update_name= '${er.updateName}',
            update_time = now()
        where ID = #{er.id}
    </update>

    <delete id="deleteDynamic" parameterType="com.ctsi.activiti.core.entity.DynamicEntity">
         update ${er.tableName}
        set deleted  = 1,
            update_by= ${er.updateBy},
            update_name= '${er.updateName}',
            update_time = now()
        where ID = #{er.id}
    </delete>



    <!--根据id集合查询业务数据  -->
    <select id="selectDataList" resultType="java.util.Map" parameterType="com.ctsi.activiti.core.entity.QueryDynamicEntity">

      select * from ${er.tableName} t where deleted = 0 and bpm_status!= 1

      <if test="er.whereSql != null and er.whereSql !='' ">
          ${er.whereSql}
      </if>


<!--      <if test="null != er.filedAndValueEntityList and er.filedAndValueEntityList.size > 1">-->
<!--            <foreach collection="er.filedAndValueEntityList" item="value" open="(" separator="," close=")">-->
<!--                $-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <foreach collection="er.queryMap"  index="key"  item="value" separator="and" open="(" close=")">-->

<!--            <if test="value != null and value !='' ">-->
<!--                 ${key} = #{value }-->
<!--            </if>-->
<!--        </foreach>-->


        order by CREATE_TIME desc
    </select>

    <select id="selectDataFormList" resultType="java.util.HashMap" parameterType="com.ctsi.activiti.core.entity.QueryDynamicEntity">
        select DISTINCT t.*,b.bpm_status AS proc_status,b.FORM_DATA_ID,b.PROC_INST_ID,b.PROC_TYPE_NAME,
                        b.FORM_DEF_ID,b.root_PROC_INST_ID AS rootProcessInstanceId,
        st.id as formTemporaryStorageId,
        case t.bpm_status when '1' then st.process_definition_key else b.MODELKEY end  as processDefinitionKey,

        case t.bpm_status when '1' then st.process_definition_id else b.PROC_DEF_ID end  as processDefinitionId,

        case t.bpm_status when '1' then st.form_id else b.FORM_DEF_ID end  as formId,

                b.id AS cscpProcBaseId,b.PROC_INST_ID AS procInstId,
        case t.bpm_status when '1' then '草稿' else ass.NODE_NAME end  as actDefUniqueName,ass.ASSIGNEE,ass.ASSIGNEE_Name,ass.task_id
        from ${er.tableName} t left join biz_form_temporary_storage  st  on st.form_data_id = t.id and st.deleted = 0
        left join cscp_proc_base b on t.process_instance_id = b.PROC_INST_ID
        left join cscp_audit_content content on b.PROC_INST_ID = content.PROC_INST_ID
        left join cscp_proc_assignee ass on b.PROC_INST_ID = ass.PROCESS_INSTANCE_ID
        where t.deleted = 0
        <!--           and t.bpm_status!= 1 -->
             <if test="er.whereSql != null and er.whereSql !='' ">
                    ${er.whereSql}
                </if>
                order by t.CREATE_TIME desc
            </select>

        </mapper>
