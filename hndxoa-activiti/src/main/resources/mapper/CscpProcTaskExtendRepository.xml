<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpProcTaskExtendRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpProcTaskExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 08 15:19:24 CST 2020.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROCINSTID" jdbcType="VARCHAR" property="procinstid" />
    <result column="PROCDEFID" jdbcType="VARCHAR" property="procdefid" />
    <result column="ACTIVITYID" jdbcType="VARCHAR" property="activityid" />
    <result column="TASKID" jdbcType="VARCHAR" property="taskid" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="DEPTID" jdbcType="VARCHAR" property="deptid" />
    <result column="DEALUSERID" jdbcType="VARCHAR" property="dealuserid" />
    <result column="DEALDEPTID" jdbcType="VARCHAR" property="dealdeptid" />
    <result column="STARTTIME" jdbcType="VARCHAR" property="starttime" />
    <result column="ENDTIME" jdbcType="VARCHAR" property="endtime" />
    <result column="CREATTIME" jdbcType="VARCHAR" property="creattime" />
    <result column="jump_type" jdbcType="VARCHAR" property="jumpType" />
    <result column="int_task_id" jdbcType="VARCHAR" property="intTaskId" />
    <result column="in_act_id" jdbcType="VARCHAR" property="inActId" />
    <result column="out_act_id" jdbcType="VARCHAR" property="outActId" />
    <result column="out_task_id" jdbcType="VARCHAR" property="outTaskId" />
    <result column="node_type" jdbcType="VARCHAR" property="nodeType" />
    <result column="LIMITTIME" jdbcType="VARCHAR" property="limittime" />
  </resultMap>
  <update id="deleteByprocessInstanceIdNew">
    update cscp_proc_task_extend set deleted = 1 where PROCINST_ID = #{processInstanceId}
  </update>

</mapper>