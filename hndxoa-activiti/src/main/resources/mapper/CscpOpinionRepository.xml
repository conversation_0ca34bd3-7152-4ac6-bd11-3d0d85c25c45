<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpOpinionRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpOpinion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 03 14:33:36 CST 2020.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="OPINION_TYPE" jdbcType="VARCHAR" property="opinionType" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="OPINION" jdbcType="VARCHAR" property="opinion" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
  </resultMap>
<select id="getOpinionPageList" resultMap="BaseResultMap">
  SELECT * FROM
  (
  SELECT ROW_NUMBER() OVER(PARTITION BY opinion ORDER BY opinion_type ASC, sort ASC) RN,
  ID, OPINION_TYPE, OPINION, sort, CREATE_TIME FROM cscp_opinion
  WHERE deleted = 0 AND ((create_by = #{userId} AND opinion_type = '0')
    OR (company_id = #{companyId} AND opinion_type = '1'))
  <if test="opinion != null and opinion != ''">
    and opinion like concat('%',concat(#{opinion},'%'))
  </if>
    ORDER BY opinion_type ASC, sort ASC
  )
  where rn = 1
</select>
</mapper>