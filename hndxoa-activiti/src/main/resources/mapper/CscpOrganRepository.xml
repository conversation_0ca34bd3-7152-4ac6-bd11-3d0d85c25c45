<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpOrganRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpOrgan">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="org_name" jdbcType="VARCHAR" property="title" />
  </resultMap>


<!--  <select id="queryOrganListByUserId" parameterType="string" resultMap="BaseResultMap">-->
<!--    SELECT o.* from cscp_user_org u LEFT JOIN cscp_org o ON u.org_id=o.id WHERE  u.user_id=#{userId,jdbcType=VARCHAR}-->
<!--  </select>-->
</mapper>