<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpCondBranchRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpCondBranch">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TARGET_ACT_ID" jdbcType="VARCHAR" property="targetActId" />
    <result column="SHOWNUM" jdbcType="VARCHAR" property="shownum" />
    <result column="MODEL_KEY" jdbcType="VARCHAR" property="modelKey" />
    <result column="CREATE_DATE" jdbcType="VARCHAR" property="createDate" />
    <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
    <result column="CURRENT_ACT_ID" jdbcType="VARCHAR" property="currentActId" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ctsi.business.domain.CscpCondBranch">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <result column="EXPRESSION" jdbcType="LONGVARCHAR" property="expression" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    ID, TARGET_ACT_ID, SHOWNUM, MODEL_KEY, CREATE_DATE, CREATE_USER, CURRENT_ACT_ID, 
    PROC_DEF_ID
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    EXPRESSION
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ctsi.business.domain.CscpCondBranchExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from CSCP_COND_BRANCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ctsi.business.domain.CscpCondBranchExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from CSCP_COND_BRANCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from CSCP_COND_BRANCH
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_COND_BRANCH
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ctsi.business.domain.CscpCondBranchExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_COND_BRANCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ctsi.business.domain.CscpCondBranch">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_COND_BRANCH (ID, TARGET_ACT_ID, SHOWNUM, 
      MODEL_KEY, CREATE_DATE, CREATE_USER, 
      CURRENT_ACT_ID, PROC_DEF_ID, EXPRESSION
      )
    values (#{id,jdbcType=VARCHAR}, #{targetActId,jdbcType=VARCHAR}, #{shownum,jdbcType=VARCHAR}, 
      #{modelKey,jdbcType=VARCHAR}, #{createDate,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{currentActId,jdbcType=VARCHAR}, #{procDefId,jdbcType=VARCHAR}, #{expression,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ctsi.business.domain.CscpCondBranch">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_COND_BRANCH
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="targetActId != null">
        TARGET_ACT_ID,
      </if>
      <if test="shownum != null">
        SHOWNUM,
      </if>
      <if test="modelKey != null">
        MODEL_KEY,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="createUser != null">
        CREATE_USER,
      </if>
      <if test="currentActId != null">
        CURRENT_ACT_ID,
      </if>
      <if test="procDefId != null">
        PROC_DEF_ID,
      </if>
      <if test="expression != null">
        EXPRESSION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="targetActId != null">
        #{targetActId,jdbcType=VARCHAR},
      </if>
      <if test="shownum != null">
        #{shownum,jdbcType=VARCHAR},
      </if>
      <if test="modelKey != null">
        #{modelKey,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="currentActId != null">
        #{currentActId,jdbcType=VARCHAR},
      </if>
      <if test="procDefId != null">
        #{procDefId,jdbcType=VARCHAR},
      </if>
      <if test="expression != null">
        #{expression,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ctsi.business.domain.CscpCondBranchExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select count(*) from CSCP_COND_BRANCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_COND_BRANCH
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.targetActId != null">
        TARGET_ACT_ID = #{record.targetActId,jdbcType=VARCHAR},
      </if>
      <if test="record.shownum != null">
        SHOWNUM = #{record.shownum,jdbcType=VARCHAR},
      </if>
      <if test="record.modelKey != null">
        MODEL_KEY = #{record.modelKey,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null">
        CREATE_DATE = #{record.createDate,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        CREATE_USER = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.currentActId != null">
        CURRENT_ACT_ID = #{record.currentActId,jdbcType=VARCHAR},
      </if>
      <if test="record.procDefId != null">
        PROC_DEF_ID = #{record.procDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.expression != null">
        EXPRESSION = #{record.expression,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_COND_BRANCH
    set ID = #{record.id,jdbcType=VARCHAR},
      TARGET_ACT_ID = #{record.targetActId,jdbcType=VARCHAR},
      SHOWNUM = #{record.shownum,jdbcType=VARCHAR},
      MODEL_KEY = #{record.modelKey,jdbcType=VARCHAR},
      CREATE_DATE = #{record.createDate,jdbcType=VARCHAR},
      CREATE_USER = #{record.createUser,jdbcType=VARCHAR},
      CURRENT_ACT_ID = #{record.currentActId,jdbcType=VARCHAR},
      PROC_DEF_ID = #{record.procDefId,jdbcType=VARCHAR},
      EXPRESSION = #{record.expression,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_COND_BRANCH
    set ID = #{record.id,jdbcType=VARCHAR},
      TARGET_ACT_ID = #{record.targetActId,jdbcType=VARCHAR},
      SHOWNUM = #{record.shownum,jdbcType=VARCHAR},
      MODEL_KEY = #{record.modelKey,jdbcType=VARCHAR},
      CREATE_DATE = #{record.createDate,jdbcType=VARCHAR},
      CREATE_USER = #{record.createUser,jdbcType=VARCHAR},
      CURRENT_ACT_ID = #{record.currentActId,jdbcType=VARCHAR},
      PROC_DEF_ID = #{record.procDefId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ctsi.business.domain.CscpCondBranch">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_COND_BRANCH
    <set>
      <if test="targetActId != null">
        TARGET_ACT_ID = #{targetActId,jdbcType=VARCHAR},
      </if>
      <if test="shownum != null">
        SHOWNUM = #{shownum,jdbcType=VARCHAR},
      </if>
      <if test="modelKey != null">
        MODEL_KEY = #{modelKey,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        CREATE_USER = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="currentActId != null">
        CURRENT_ACT_ID = #{currentActId,jdbcType=VARCHAR},
      </if>
      <if test="procDefId != null">
        PROC_DEF_ID = #{procDefId,jdbcType=VARCHAR},
      </if>
      <if test="expression != null">
        EXPRESSION = #{expression,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ctsi.business.domain.CscpCondBranch">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_COND_BRANCH
    set TARGET_ACT_ID = #{targetActId,jdbcType=VARCHAR},
      SHOWNUM = #{shownum,jdbcType=VARCHAR},
      MODEL_KEY = #{modelKey,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=VARCHAR},
      CREATE_USER = #{createUser,jdbcType=VARCHAR},
      CURRENT_ACT_ID = #{currentActId,jdbcType=VARCHAR},
      PROC_DEF_ID = #{procDefId,jdbcType=VARCHAR},
      EXPRESSION = #{expression,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ctsi.business.domain.CscpCondBranch">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_COND_BRANCH
    set TARGET_ACT_ID = #{targetActId,jdbcType=VARCHAR},
      SHOWNUM = #{shownum,jdbcType=VARCHAR},
      MODEL_KEY = #{modelKey,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=VARCHAR},
      CREATE_USER = #{createUser,jdbcType=VARCHAR},
      CURRENT_ACT_ID = #{currentActId,jdbcType=VARCHAR},
      PROC_DEF_ID = #{procDefId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>