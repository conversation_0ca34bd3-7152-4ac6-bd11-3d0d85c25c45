<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpProcTypeRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpProcType">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 10 09:16:39 CST 2019.
    -->
    <id column="TYPE_ID" jdbcType="VARCHAR" property="typeId" />
    <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
    <result column="PARENT_TYPE_ID" jdbcType="VARCHAR" property="parentTypeId" />
    <result column="APP_CODE" jdbcType="VARCHAR" property="appCode" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
  </resultMap>

</mapper>