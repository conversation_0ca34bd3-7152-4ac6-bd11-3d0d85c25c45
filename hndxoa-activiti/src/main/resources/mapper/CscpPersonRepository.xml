<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpPersonRepository">
    <resultMap id="BaseResultMap" type="com.ctsi.ssdc.admin.domain.CscpUser">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="username" jdbcType="VARCHAR" property="username" />
    </resultMap>

    <select id="queryUserListByOrgId" parameterType="string" resultMap="BaseResultMap">
        SELECT DISTINCT(u.id),u.username from cscp_user u LEFT JOIN cscp_user_org o ON u.id=o.user_id WHERE org_id=#{parentId,jdbcType=VARCHAR}
    </select>

    <select id="queryUserListByGroupId" parameterType="string" resultMap="BaseResultMap">
        SELECT DISTINCT(u.id),u.username from cscp_user u LEFT JOIN cscp_user_work_group o ON u.id=o.user_id WHERE group_id=#{parentId,jdbcType=VARCHAR}
    </select>
</mapper>