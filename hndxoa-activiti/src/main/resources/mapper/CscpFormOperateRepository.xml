<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpFormOperateRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpFormOperate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
    <result column="ACT_DEF_ID" jdbcType="VARCHAR" property="actDefId" />
    <result column="FORM_DEF_ID" jdbcType="VARCHAR" property="formDefId" />
    <result column="BTN_DEF_ID" jdbcType="VARCHAR" property="btnDefId" />
    <result column="BTN_DEF_NAME" jdbcType="VARCHAR" property="btnDefName" />
    <result column="TASK_TYPE" jdbcType="VARCHAR" property="taskType" />
    <result column="DISPLAY_ORDER" jdbcType="INTEGER" property="displayOrder" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    ID, PROC_DEF_ID, ACT_DEF_ID, FORM_DEF_ID, BTN_DEF_ID, BTN_DEF_NAME, TASK_TYPE, DISPLAY_ORDER
  </sql>
  <select id="selectByExample" parameterType="com.ctsi.business.domain.CscpFormOperateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from CSCP_FORM_OPERATE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from CSCP_FORM_OPERATE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_FORM_OPERATE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ctsi.business.domain.CscpFormOperateExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    delete from CSCP_FORM_OPERATE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ctsi.business.domain.CscpFormOperate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_FORM_OPERATE (ID, PROC_DEF_ID, ACT_DEF_ID, 
      FORM_DEF_ID, BTN_DEF_ID, BTN_DEF_NAME, 
      TASK_TYPE, DISPLAY_ORDER)
    values (#{id,jdbcType=VARCHAR}, #{procDefId,jdbcType=VARCHAR}, #{actDefId,jdbcType=VARCHAR}, 
      #{formDefId,jdbcType=VARCHAR}, #{btnDefId,jdbcType=VARCHAR}, #{btnDefName,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=VARCHAR}, #{displayOrder,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ctsi.business.domain.CscpFormOperate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    insert into CSCP_FORM_OPERATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="procDefId != null">
        PROC_DEF_ID,
      </if>
      <if test="actDefId != null">
        ACT_DEF_ID,
      </if>
      <if test="formDefId != null">
        FORM_DEF_ID,
      </if>
      <if test="btnDefId != null">
        BTN_DEF_ID,
      </if>
      <if test="btnDefName != null">
        BTN_DEF_NAME,
      </if>
      <if test="taskType != null">
        TASK_TYPE,
      </if>
      <if test="displayOrder != null">
        DISPLAY_ORDER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="procDefId != null">
        #{procDefId,jdbcType=VARCHAR},
      </if>
      <if test="actDefId != null">
        #{actDefId,jdbcType=VARCHAR},
      </if>
      <if test="formDefId != null">
        #{formDefId,jdbcType=VARCHAR},
      </if>
      <if test="btnDefId != null">
        #{btnDefId,jdbcType=VARCHAR},
      </if>
      <if test="btnDefName != null">
        #{btnDefName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="displayOrder != null">
        #{displayOrder,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ctsi.business.domain.CscpFormOperateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    select count(*) from CSCP_FORM_OPERATE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_FORM_OPERATE
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.procDefId != null">
        PROC_DEF_ID = #{record.procDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.actDefId != null">
        ACT_DEF_ID = #{record.actDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.formDefId != null">
        FORM_DEF_ID = #{record.formDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.btnDefId != null">
        BTN_DEF_ID = #{record.btnDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.btnDefName != null">
        BTN_DEF_NAME = #{record.btnDefName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        TASK_TYPE = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.displayOrder != null">
        DISPLAY_ORDER = #{record.displayOrder,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_FORM_OPERATE
    set ID = #{record.id,jdbcType=VARCHAR},
      PROC_DEF_ID = #{record.procDefId,jdbcType=VARCHAR},
      ACT_DEF_ID = #{record.actDefId,jdbcType=VARCHAR},
      FORM_DEF_ID = #{record.formDefId,jdbcType=VARCHAR},
      BTN_DEF_ID = #{record.btnDefId,jdbcType=VARCHAR},
      BTN_DEF_NAME = #{record.btnDefName,jdbcType=VARCHAR},
      TASK_TYPE = #{record.taskType,jdbcType=VARCHAR},
      DISPLAY_ORDER = #{record.displayOrder,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ctsi.business.domain.CscpFormOperate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_FORM_OPERATE
    <set>
      <if test="procDefId != null">
        PROC_DEF_ID = #{procDefId,jdbcType=VARCHAR},
      </if>
      <if test="actDefId != null">
        ACT_DEF_ID = #{actDefId,jdbcType=VARCHAR},
      </if>
      <if test="formDefId != null">
        FORM_DEF_ID = #{formDefId,jdbcType=VARCHAR},
      </if>
      <if test="btnDefId != null">
        BTN_DEF_ID = #{btnDefId,jdbcType=VARCHAR},
      </if>
      <if test="btnDefName != null">
        BTN_DEF_NAME = #{btnDefName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="displayOrder != null">
        DISPLAY_ORDER = #{displayOrder,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ctsi.business.domain.CscpFormOperate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    update CSCP_FORM_OPERATE
    set PROC_DEF_ID = #{procDefId,jdbcType=VARCHAR},
      ACT_DEF_ID = #{actDefId,jdbcType=VARCHAR},
      FORM_DEF_ID = #{formDefId,jdbcType=VARCHAR},
      BTN_DEF_ID = #{btnDefId,jdbcType=VARCHAR},
      BTN_DEF_NAME = #{btnDefName,jdbcType=VARCHAR},
      TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      DISPLAY_ORDER = #{displayOrder,jdbcType=INTEGER}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>