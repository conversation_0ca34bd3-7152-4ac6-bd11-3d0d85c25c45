<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpBtnRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpBtn">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 02 16:09:19 CST 2020.
    -->
    <id column="ACTION_ID" jdbcType="VARCHAR" property="actionId" />
    <result column="ACTION_NAME" jdbcType="VARCHAR" property="actionName" />
    <result column="ACTION_TYPE" jdbcType="VARCHAR" property="actionType" />
    <result column="FUNCTION_NAME" jdbcType="VARCHAR" property="functionName" />
    <result column="DISPLAY_ORDER" jdbcType="INTEGER" property="displayOrder" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="CLASS_NAME" jdbcType="VARCHAR" property="className" />
    <result column="SUB_TYPE" jdbcType="VARCHAR" property="subType" />
    <result column="IS_DEFAULT" jdbcType="VARCHAR" property="isDefault" />
  </resultMap>

</mapper>