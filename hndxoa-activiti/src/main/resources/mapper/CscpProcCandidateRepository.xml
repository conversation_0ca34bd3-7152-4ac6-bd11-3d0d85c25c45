<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpProcCandidateRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpProcCandidate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
    <result column="ACT_DEF_ID" jdbcType="VARCHAR" property="actDefId" />
    <result column="USER_TYPE" jdbcType="VARCHAR" property="userType" />
    <result column="ACT_TYPE" jdbcType="VARCHAR" property="actType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ctsi.business.domain.CscpProcCandidate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <result column="CANDIDATE" jdbcType="LONGVARCHAR" property="candidate" />
  </resultMap>

</mapper>