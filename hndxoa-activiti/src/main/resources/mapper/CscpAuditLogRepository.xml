<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpAuditLogRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpAuditLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 25 15:02:49 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
    <result column="PROC_INST_ID" jdbcType="VARCHAR" property="procInstId" />
    <result column="ACT_ID" jdbcType="VARCHAR" property="actId" />
    <result column="ACT_NAME" jdbcType="VARCHAR" property="actName" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="AUDITOR_ID" jdbcType="VARCHAR" property="auditorId" />
    <result column="AUDITOR_NAME" jdbcType="VARCHAR" property="auditorName" />
    <result column="AUDIT_TIME" jdbcType="VARCHAR" property="auditTime" />
    <result column="DELEGATOR_ID" jdbcType="VARCHAR" property="delegatorId" />
    <result column="DELEGATOR_NAME" jdbcType="VARCHAR" property="delegatorName" />
    <result column="AUDIT_CONTENT" jdbcType="VARCHAR" property="auditContent" />
    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
    <result column="PROCESS_STATUS" jdbcType="VARCHAR" property="processStatus" />
    <result column="OPERATE_TYPE" jdbcType="VARCHAR" property="operateType" />
    <result column="CLIENT_TYPE" jdbcType="VARCHAR" property="clientType" />
    <result column="EXT1" jdbcType="VARCHAR" property="ext1" />
    <result column="EXT2" jdbcType="VARCHAR" property="ext2" />
    <result column="EXT3" jdbcType="VARCHAR" property="ext3" />
    <result column="EXT4" jdbcType="VARCHAR" property="ext4" />
    <result column="EXT5" jdbcType="VARCHAR" property="ext5" />
  </resultMap>
<!--  <sql id="Example_Where_Clause">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    <where>-->
<!--      <foreach collection="oredCriteria" item="criteria" separator="or">-->
<!--        <if test="criteria.valid">-->
<!--          <trim prefix="(" prefixOverrides="and" suffix=")">-->
<!--            <foreach collection="criteria.criteria" item="criterion">-->
<!--              <choose>-->
<!--                <when test="criterion.noValue">-->
<!--                  and ${criterion.condition}-->
<!--                </when>-->
<!--                <when test="criterion.singleValue">-->
<!--                  and ${criterion.condition} #{criterion.value}-->
<!--                </when>-->
<!--                <when test="criterion.betweenValue">-->
<!--                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}-->
<!--                </when>-->
<!--                <when test="criterion.listValue">-->
<!--                  and ${criterion.condition}-->
<!--                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">-->
<!--                    #{listItem}-->
<!--                  </foreach>-->
<!--                </when>-->
<!--              </choose>-->
<!--            </foreach>-->
<!--          </trim>-->
<!--        </if>-->
<!--      </foreach>-->
<!--    </where>-->
<!--  </sql>-->
<!--  <sql id="Update_By_Example_Where_Clause">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    <where>-->
<!--      <foreach collection="example.oredCriteria" item="criteria" separator="or">-->
<!--        <if test="criteria.valid">-->
<!--          <trim prefix="(" prefixOverrides="and" suffix=")">-->
<!--            <foreach collection="criteria.criteria" item="criterion">-->
<!--              <choose>-->
<!--                <when test="criterion.noValue">-->
<!--                  and ${criterion.condition}-->
<!--                </when>-->
<!--                <when test="criterion.singleValue">-->
<!--                  and ${criterion.condition} #{criterion.value}-->
<!--                </when>-->
<!--                <when test="criterion.betweenValue">-->
<!--                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}-->
<!--                </when>-->
<!--                <when test="criterion.listValue">-->
<!--                  and ${criterion.condition}-->
<!--                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">-->
<!--                    #{listItem}-->
<!--                  </foreach>-->
<!--                </when>-->
<!--              </choose>-->
<!--            </foreach>-->
<!--          </trim>-->
<!--        </if>-->
<!--      </foreach>-->
<!--    </where>-->
<!--  </sql>-->
<!--  <sql id="Base_Column_List">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    ID, PROC_DEF_ID, PROC_INST_ID, ACT_ID, ACT_NAME, TITLE, AUDITOR_ID, AUDITOR_NAME, -->
<!--    AUDIT_TIME, DELEGATOR_ID, DELEGATOR_NAME, AUDIT_CONTENT, AUDIT_STATUS, PROCESS_STATUS, -->
<!--    OPERATE_TYPE, CLIENT_TYPE, EXT1, EXT2, EXT3, EXT4, EXT5-->
<!--  </sql>-->
<!--  <select id="selectByExample" parameterType="com.ctsi.business.domain.CscpAuditLogExample" resultMap="BaseResultMap">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    select-->
<!--    <if test="distinct">-->
<!--      distinct-->
<!--    </if>-->
<!--    <include refid="Base_Column_List" />-->
<!--    from cscp_audit_log-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--    <if test="orderByClause != null">-->
<!--      order by ${orderByClause}-->
<!--    </if>-->
<!--  </select>-->
<!--  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    select -->
<!--    <include refid="Base_Column_List" />-->
<!--    from cscp_audit_log-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </select>-->
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    delete from cscp_audit_log-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </delete>-->
<!--  <delete id="deleteByExample" parameterType="com.ctsi.business.domain.CscpAuditLogExample">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    delete from cscp_audit_log-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--  </delete>-->
<!--  <insert id="insert" parameterType="com.ctsi.business.domain.CscpAuditLog">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    insert into cscp_audit_log (ID, PROC_DEF_ID, PROC_INST_ID, -->
<!--      ACT_ID, ACT_NAME, TITLE, -->
<!--      AUDITOR_ID, AUDITOR_NAME, AUDIT_TIME, -->
<!--      DELEGATOR_ID, DELEGATOR_NAME, AUDIT_CONTENT, -->
<!--      AUDIT_STATUS, PROCESS_STATUS, OPERATE_TYPE, -->
<!--      CLIENT_TYPE, EXT1, EXT2, -->
<!--      EXT3, EXT4, EXT5)-->
<!--    values (#{id,jdbcType=VARCHAR}, #{procDefId,jdbcType=VARCHAR}, #{procInstId,jdbcType=VARCHAR}, -->
<!--      #{actId,jdbcType=VARCHAR}, #{actName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, -->
<!--      #{auditorId,jdbcType=VARCHAR}, #{auditorName,jdbcType=VARCHAR}, #{auditTime,jdbcType=VARCHAR}, -->
<!--      #{delegatorId,jdbcType=VARCHAR}, #{delegatorName,jdbcType=VARCHAR}, #{auditContent,jdbcType=VARCHAR}, -->
<!--      #{auditStatus,jdbcType=VARCHAR}, #{processStatus,jdbcType=VARCHAR}, #{operateType,jdbcType=VARCHAR}, -->
<!--      #{clientType,jdbcType=VARCHAR}, #{ext1,jdbcType=VARCHAR}, #{ext2,jdbcType=VARCHAR}, -->
<!--      #{ext3,jdbcType=VARCHAR}, #{ext4,jdbcType=VARCHAR}, #{ext5,jdbcType=VARCHAR})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.ctsi.business.domain.CscpAuditLog">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    insert into cscp_audit_log-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        ID,-->
<!--      </if>-->
<!--      <if test="procDefId != null">-->
<!--        PROC_DEF_ID,-->
<!--      </if>-->
<!--      <if test="procInstId != null">-->
<!--        PROC_INST_ID,-->
<!--      </if>-->
<!--      <if test="actId != null">-->
<!--        ACT_ID,-->
<!--      </if>-->
<!--      <if test="actName != null">-->
<!--        ACT_NAME,-->
<!--      </if>-->
<!--      <if test="title != null">-->
<!--        TITLE,-->
<!--      </if>-->
<!--      <if test="auditorId != null">-->
<!--        AUDITOR_ID,-->
<!--      </if>-->
<!--      <if test="auditorName != null">-->
<!--        AUDITOR_NAME,-->
<!--      </if>-->
<!--      <if test="auditTime != null">-->
<!--        AUDIT_TIME,-->
<!--      </if>-->
<!--      <if test="delegatorId != null">-->
<!--        DELEGATOR_ID,-->
<!--      </if>-->
<!--      <if test="delegatorName != null">-->
<!--        DELEGATOR_NAME,-->
<!--      </if>-->
<!--      <if test="auditContent != null">-->
<!--        AUDIT_CONTENT,-->
<!--      </if>-->
<!--      <if test="auditStatus != null">-->
<!--        AUDIT_STATUS,-->
<!--      </if>-->
<!--      <if test="processStatus != null">-->
<!--        PROCESS_STATUS,-->
<!--      </if>-->
<!--      <if test="operateType != null">-->
<!--        OPERATE_TYPE,-->
<!--      </if>-->
<!--      <if test="clientType != null">-->
<!--        CLIENT_TYPE,-->
<!--      </if>-->
<!--      <if test="ext1 != null">-->
<!--        EXT1,-->
<!--      </if>-->
<!--      <if test="ext2 != null">-->
<!--        EXT2,-->
<!--      </if>-->
<!--      <if test="ext3 != null">-->
<!--        EXT3,-->
<!--      </if>-->
<!--      <if test="ext4 != null">-->
<!--        EXT4,-->
<!--      </if>-->
<!--      <if test="ext5 != null">-->
<!--        EXT5,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="procDefId != null">-->
<!--        #{procDefId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="procInstId != null">-->
<!--        #{procInstId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="actId != null">-->
<!--        #{actId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="actName != null">-->
<!--        #{actName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="title != null">-->
<!--        #{title,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditorId != null">-->
<!--        #{auditorId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditorName != null">-->
<!--        #{auditorName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditTime != null">-->
<!--        #{auditTime,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="delegatorId != null">-->
<!--        #{delegatorId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="delegatorName != null">-->
<!--        #{delegatorName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditContent != null">-->
<!--        #{auditContent,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditStatus != null">-->
<!--        #{auditStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="processStatus != null">-->
<!--        #{processStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operateType != null">-->
<!--        #{operateType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="clientType != null">-->
<!--        #{clientType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext1 != null">-->
<!--        #{ext1,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext2 != null">-->
<!--        #{ext2,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext3 != null">-->
<!--        #{ext3,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext4 != null">-->
<!--        #{ext4,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext5 != null">-->
<!--        #{ext5,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <select id="countByExample" parameterType="com.ctsi.business.domain.CscpAuditLogExample" resultType="java.lang.Long">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    select count(*) from cscp_audit_log-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--  </select>-->
<!--  <update id="updateByExampleSelective" parameterType="map">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    update cscp_audit_log-->
<!--    <set>-->
<!--      <if test="record.id != null">-->
<!--        ID = #{record.id,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.procDefId != null">-->
<!--        PROC_DEF_ID = #{record.procDefId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.procInstId != null">-->
<!--        PROC_INST_ID = #{record.procInstId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.actId != null">-->
<!--        ACT_ID = #{record.actId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.actName != null">-->
<!--        ACT_NAME = #{record.actName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.title != null">-->
<!--        TITLE = #{record.title,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.auditorId != null">-->
<!--        AUDITOR_ID = #{record.auditorId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.auditorName != null">-->
<!--        AUDITOR_NAME = #{record.auditorName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.auditTime != null">-->
<!--        AUDIT_TIME = #{record.auditTime,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.delegatorId != null">-->
<!--        DELEGATOR_ID = #{record.delegatorId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.delegatorName != null">-->
<!--        DELEGATOR_NAME = #{record.delegatorName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.auditContent != null">-->
<!--        AUDIT_CONTENT = #{record.auditContent,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.auditStatus != null">-->
<!--        AUDIT_STATUS = #{record.auditStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.processStatus != null">-->
<!--        PROCESS_STATUS = #{record.processStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.operateType != null">-->
<!--        OPERATE_TYPE = #{record.operateType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.clientType != null">-->
<!--        CLIENT_TYPE = #{record.clientType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.ext1 != null">-->
<!--        EXT1 = #{record.ext1,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.ext2 != null">-->
<!--        EXT2 = #{record.ext2,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.ext3 != null">-->
<!--        EXT3 = #{record.ext3,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.ext4 != null">-->
<!--        EXT4 = #{record.ext4,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.ext5 != null">-->
<!--        EXT5 = #{record.ext5,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </set>-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Update_By_Example_Where_Clause" />-->
<!--    </if>-->
<!--  </update>-->
<!--  <update id="updateByExample" parameterType="map">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    update cscp_audit_log-->
<!--    set ID = #{record.id,jdbcType=VARCHAR},-->
<!--      PROC_DEF_ID = #{record.procDefId,jdbcType=VARCHAR},-->
<!--      PROC_INST_ID = #{record.procInstId,jdbcType=VARCHAR},-->
<!--      ACT_ID = #{record.actId,jdbcType=VARCHAR},-->
<!--      ACT_NAME = #{record.actName,jdbcType=VARCHAR},-->
<!--      TITLE = #{record.title,jdbcType=VARCHAR},-->
<!--      AUDITOR_ID = #{record.auditorId,jdbcType=VARCHAR},-->
<!--      AUDITOR_NAME = #{record.auditorName,jdbcType=VARCHAR},-->
<!--      AUDIT_TIME = #{record.auditTime,jdbcType=VARCHAR},-->
<!--      DELEGATOR_ID = #{record.delegatorId,jdbcType=VARCHAR},-->
<!--      DELEGATOR_NAME = #{record.delegatorName,jdbcType=VARCHAR},-->
<!--      AUDIT_CONTENT = #{record.auditContent,jdbcType=VARCHAR},-->
<!--      AUDIT_STATUS = #{record.auditStatus,jdbcType=VARCHAR},-->
<!--      PROCESS_STATUS = #{record.processStatus,jdbcType=VARCHAR},-->
<!--      OPERATE_TYPE = #{record.operateType,jdbcType=VARCHAR},-->
<!--      CLIENT_TYPE = #{record.clientType,jdbcType=VARCHAR},-->
<!--      EXT1 = #{record.ext1,jdbcType=VARCHAR},-->
<!--      EXT2 = #{record.ext2,jdbcType=VARCHAR},-->
<!--      EXT3 = #{record.ext3,jdbcType=VARCHAR},-->
<!--      EXT4 = #{record.ext4,jdbcType=VARCHAR},-->
<!--      EXT5 = #{record.ext5,jdbcType=VARCHAR}-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Update_By_Example_Where_Clause" />-->
<!--    </if>-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.ctsi.business.domain.CscpAuditLog">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    update cscp_audit_log-->
<!--    <set>-->
<!--      <if test="procDefId != null">-->
<!--        PROC_DEF_ID = #{procDefId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="procInstId != null">-->
<!--        PROC_INST_ID = #{procInstId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="actId != null">-->
<!--        ACT_ID = #{actId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="actName != null">-->
<!--        ACT_NAME = #{actName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="title != null">-->
<!--        TITLE = #{title,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditorId != null">-->
<!--        AUDITOR_ID = #{auditorId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditorName != null">-->
<!--        AUDITOR_NAME = #{auditorName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditTime != null">-->
<!--        AUDIT_TIME = #{auditTime,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="delegatorId != null">-->
<!--        DELEGATOR_ID = #{delegatorId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="delegatorName != null">-->
<!--        DELEGATOR_NAME = #{delegatorName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditContent != null">-->
<!--        AUDIT_CONTENT = #{auditContent,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="auditStatus != null">-->
<!--        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="processStatus != null">-->
<!--        PROCESS_STATUS = #{processStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operateType != null">-->
<!--        OPERATE_TYPE = #{operateType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="clientType != null">-->
<!--        CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext1 != null">-->
<!--        EXT1 = #{ext1,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext2 != null">-->
<!--        EXT2 = #{ext2,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext3 != null">-->
<!--        EXT3 = #{ext3,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext4 != null">-->
<!--        EXT4 = #{ext4,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="ext5 != null">-->
<!--        EXT5 = #{ext5,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </set>-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.ctsi.business.domain.CscpAuditLog">-->
<!--    &lt;!&ndash;-->
<!--      WARNING - @mbg.generated-->
<!--      This element is automatically generated by MyBatis Generator, do not modify.-->
<!--      This element was generated on Wed Dec 25 15:02:49 CST 2019.-->
<!--    &ndash;&gt;-->
<!--    update cscp_audit_log-->
<!--    set PROC_DEF_ID = #{procDefId,jdbcType=VARCHAR},-->
<!--      PROC_INST_ID = #{procInstId,jdbcType=VARCHAR},-->
<!--      ACT_ID = #{actId,jdbcType=VARCHAR},-->
<!--      ACT_NAME = #{actName,jdbcType=VARCHAR},-->
<!--      TITLE = #{title,jdbcType=VARCHAR},-->
<!--      AUDITOR_ID = #{auditorId,jdbcType=VARCHAR},-->
<!--      AUDITOR_NAME = #{auditorName,jdbcType=VARCHAR},-->
<!--      AUDIT_TIME = #{auditTime,jdbcType=VARCHAR},-->
<!--      DELEGATOR_ID = #{delegatorId,jdbcType=VARCHAR},-->
<!--      DELEGATOR_NAME = #{delegatorName,jdbcType=VARCHAR},-->
<!--      AUDIT_CONTENT = #{auditContent,jdbcType=VARCHAR},-->
<!--      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},-->
<!--      PROCESS_STATUS = #{processStatus,jdbcType=VARCHAR},-->
<!--      OPERATE_TYPE = #{operateType,jdbcType=VARCHAR},-->
<!--      CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},-->
<!--      EXT1 = #{ext1,jdbcType=VARCHAR},-->
<!--      EXT2 = #{ext2,jdbcType=VARCHAR},-->
<!--      EXT3 = #{ext3,jdbcType=VARCHAR},-->
<!--      EXT4 = #{ext4,jdbcType=VARCHAR},-->
<!--      EXT5 = #{ext5,jdbcType=VARCHAR}-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </update>-->


<!--  <select id="getAuditLogList" parameterType="java.lang.String" resultMap="BaseResultMap">-->
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from cscp_audit_log-->
<!--    where PROC_INST_ID = #{PROCESS_INSTANCE_ID,jdbcType=VARCHAR}-->
<!--    order by AUDIT_TIME desc-->
<!--  </select>-->
</mapper>