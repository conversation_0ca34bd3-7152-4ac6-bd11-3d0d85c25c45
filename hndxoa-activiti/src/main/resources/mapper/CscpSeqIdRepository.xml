<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpSeqIdRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpSeqId">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 23 14:23:32 CST 2019.
    -->
    <id column="SEQ_ID" jdbcType="VARCHAR" property="seqId" />
    <result column="SEQ_NAME" jdbcType="VARCHAR" property="seqName" />
    <result column="SEQ_ALIAS" jdbcType="VARCHAR" property="seqAlias" />
    <result column="CUR_DATE" jdbcType="VARCHAR" property="curDate" />
    <result column="SEQ_RULE" jdbcType="VARCHAR" property="seqRule" />
    <result column="RULE_CONF" jdbcType="VARCHAR" property="ruleConf" />
    <result column="INIT_VAL" jdbcType="INTEGER" property="initVal" />
    <result column="GEN_TYPE" jdbcType="VARCHAR" property="genType" />
    <result column="SEQ_LEN" jdbcType="INTEGER" property="seqLen" />
    <result column="CUR_VAL" jdbcType="INTEGER" property="curVal" />
    <result column="STEP" jdbcType="INTEGER" property="step" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="IS_DEFAULT" jdbcType="VARCHAR" property="isDefault" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ctsi.business.domain.CscpSeqId">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 23 14:23:32 CST 2019.
    -->
    <result column="RULE_JSON" jdbcType="LONGVARCHAR" property="ruleJson" />
  </resultMap>


</mapper>