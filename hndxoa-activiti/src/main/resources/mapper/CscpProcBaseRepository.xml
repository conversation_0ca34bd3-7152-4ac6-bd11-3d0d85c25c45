<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpProcBaseRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpProcBase">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROC_INST_ID" jdbcType="VARCHAR" property="procInstId" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
    <result column="FORM_DEF_ID" jdbcType="VARCHAR" property="formDefId" />
    <result column="FORM_DATA_ID" jdbcType="VARCHAR" property="formDataId" />
    <result column="TASK_TITLE" jdbcType="VARCHAR" property="taskTitle" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
    <result column="PROC_CREATE_ID" jdbcType="VARCHAR" property="procCreateId" />
    <result column="PROC_CREATE_NAME" jdbcType="VARCHAR" property="procCreateName" />
    <result column="PROC_CREATE_DEPTNAME" jdbcType="VARCHAR" property="procCreateDeptname" />
    <result column="PROC_CREATE_DEPTID" jdbcType="VARCHAR" property="procCreateDeptid" />
    <result column="PROC_STATUES" jdbcType="VARCHAR" property="procStatues" />
    <result column="PROC_END_USERID" jdbcType="VARCHAR" property="procEndUserid" />
    <result column="PROC_END_USERNAME" jdbcType="VARCHAR" property="procEndUsername" />
    <result column="PROC_END_DEPTID" jdbcType="VARCHAR" property="procEndDeptid" />
    <result column="PROC_END_DEPTNAME" jdbcType="VARCHAR" property="procEndDeptname" />
    <result column="PROC_END_TIME" jdbcType="VARCHAR" property="procEndTime" />
    <result column="ACT_DEF_UNIQUE_ID" jdbcType="VARCHAR" property="actDefUniqueId" />
    <result column="ACT_DEF_UNIQUE_NAME" jdbcType="VARCHAR" property="actDefUniqueName" />
    <result column="PROC_SEQ_NO" jdbcType="VARCHAR" property="procSeqNo" />
    <result column="PROC_TYPE_ID" jdbcType="VARCHAR" property="procTypeId" />
    <result column="PROC_IS_SHOW" jdbcType="VARCHAR" property="procIsShow" />
    <result column="BID" jdbcType="VARCHAR" property="bid" />
    <result column="MODELKEY" jdbcType="VARCHAR" property="modelkey" />
    <result column="PROC_TYPE" jdbcType="VARCHAR" property="procType" />
    <result column="TYPE_NAME" jdbcType="VARCHAR" property="procTypeName" />
    <result column="FORM_MAIN_NAME" jdbcType="VARCHAR" property="formMainName" />
    <result column="EXT1" jdbcType="VARCHAR" property="ext1" />
    <result column="EXT2" jdbcType="VARCHAR" property="ext2" />
    <result column="EXT3" jdbcType="VARCHAR" property="ext3" />
    <result column="EXT4" jdbcType="VARCHAR" property="ext4" />
    <result column="EXT5" jdbcType="VARCHAR" property="ext5" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ctsi.business.domain.CscpProcBase">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <result column="VARI_EXT" jdbcType="LONGVARCHAR" property="variExt" />
  </resultMap>

    <resultMap id="taskVOResultMap" type="com.ctsi.activiti.core.vo.TaskVO">

        <result column="title" jdbcType="VARCHAR" property="title" />
        <result column="bpm_status" jdbcType="INTEGER" property="bpmStatus" />
        <result column="document" jdbcType="INTEGER" property="document" />
        <result column="annex" jdbcType="INTEGER" property="annex" />
        <result column="FORM_DATA_ID" jdbcType="VARCHAR" property="formDataId" />
        <result column="FORM_DEF_ID" jdbcType="VARCHAR" property="formId" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="processCreatTime" />
        <result column="process_end_time" jdbcType="TIMESTAMP" property="processEndTime" />
        <result column="handleTime" jdbcType="TIMESTAMP" property="handleTime" />

        <result column="create_name" jdbcType="VARCHAR" property="createName" />
        <result column="create_by" jdbcType="INTEGER" property="createBy" />
<!--
        <result column="PROC_TYPE_ID" jdbcType="VARCHAR" property="typeId" />
-->
        <result column="PROC_TYPE_NAME" jdbcType="VARCHAR" property="procTypeName" />

        <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="processDefinitionId" />
        <result column="PROC_INST_ID" jdbcType="VARCHAR" property="processInstanceId" />
       <!-- <result column="ACT_DEF_UNIQUE_ID" jdbcType="VARCHAR" property="actDefUniqueId" />
        <result column="ACT_DEF_UNIQUE_NAME" jdbcType="VARCHAR" property="actDefUniqueName" />-->
        <result column="MODELKEY" jdbcType="VARCHAR" property="processDefinitionKey" />
        <result column="act_def_unique_name" jdbcType="VARCHAR" property="name" />
        <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
        <result column="ASSIGNEE_Name" jdbcType="VARCHAR" property="assigneeName" />
        <result column="ASSIGNEE" jdbcType="VARCHAR" property="assingee" />

        <result column="NODE_Name" jdbcType="VARCHAR" property="nodeName" />
        <result column="root_PROC_INST_ID" jdbcType="VARCHAR" property="rootProcessInstanceId" />
        <result column="task_id" jdbcType="INTEGER" property="taskId" />
        <result column="read_status" jdbcType="INTEGER" property="readStatus" />
        <result column="is_circularize" jdbcType="INTEGER" property="isCircularize" />
        <result column="review_comments" jdbcType="VARCHAR" property="reviewComments" />
        <result column="baseId" jdbcType="INTEGER" property="baseId" />
        <result column="taskDefinitionKey" jdbcType="VARCHAR" property="taskDefinitionKey" />
    </resultMap>

    <resultMap id="processMonitorDTOResultMap" type="com.ctsi.business.dto.QueryAllProcessMonitorDTO">

        <result column="title" jdbcType="VARCHAR" property="title" />
        <result column="bpm_status" jdbcType="INTEGER" property="bpmStatus" />
        <result column="document" jdbcType="INTEGER" property="document" />
        <result column="annex" jdbcType="INTEGER" property="annex" />
        <result column="FORM_DATA_ID" jdbcType="VARCHAR" property="formDataId" />
        <result column="FORM_DEF_ID" jdbcType="VARCHAR" property="formId" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="processCreatTime" />
        <result column="process_end_time" jdbcType="TIMESTAMP" property="processEndTime" />
        <result column="handleTime" jdbcType="TIMESTAMP" property="handleTime" />

        <result column="create_name" jdbcType="VARCHAR" property="createName" />
        <result column="create_by" jdbcType="INTEGER" property="createBy" />
        <!--
                <result column="PROC_TYPE_ID" jdbcType="VARCHAR" property="typeId" />
        -->
        <result column="PROC_TYPE_NAME" jdbcType="VARCHAR" property="procTypeName" />

        <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="processDefinitionId" />
        <result column="PROC_INST_ID" jdbcType="VARCHAR" property="processInstanceId" />
        <!-- <result column="ACT_DEF_UNIQUE_ID" jdbcType="VARCHAR" property="actDefUniqueId" />
         <result column="ACT_DEF_UNIQUE_NAME" jdbcType="VARCHAR" property="actDefUniqueName" />-->
        <result column="MODELKEY" jdbcType="VARCHAR" property="processDefinitionKey" />
        <result column="act_def_unique_name" jdbcType="VARCHAR" property="name" />
        <result column="department_name" jdbcType="VARCHAR" property="departmentName" />

        <result column="ASSIGNEE" jdbcType="VARCHAR" property="assingee" />
        <result column="ASSIGNEE_Name" jdbcType="VARCHAR" property="assigneeName" />
        <result column="root_PROC_INST_ID" jdbcType="VARCHAR" property="rootProcessInstanceId" />
        <result column="task_id" jdbcType="INTEGER" property="taskId" />
        <result column="read_status" jdbcType="INTEGER" property="readStatus" />
        <result column="is_circularize" jdbcType="INTEGER" property="isCircularize" />
        <result column="review_comments" jdbcType="VARCHAR" property="reviewComments" />
        <result column="baseId" jdbcType="INTEGER" property="baseId" />
        <result column="taskDefinitionKey" jdbcType="VARCHAR" property="taskDefinitionKey" />
    </resultMap>

    <update id="deleteForwardRecordByprocessInstanceId">
        update biz_approval_forward_record
        set deleted = 1
        where
        process_instance_id = #{processInstanceId}
    </update>


    <select id="queryPageCscpProcBaseForPage" parameterType="com.ctsi.business.domain.CscpProcBase" resultMap="BaseResultMap">
        select b.ID, b.PROC_INST_ID, b.PROC_DEF_ID, b.FORM_DEF_ID, b.FORM_DATA_ID, b.TASK_TITLE, b.CREATE_TIME, b.
        PROC_CREATE_ID, b.PROC_CREATE_NAME, b.PROC_CREATE_DEPTNAME, b.PROC_CREATE_DEPTID, b.PROC_STATUES, b.
        PROC_END_USERID, b.PROC_END_USERNAME, b.PROC_END_DEPTID, b.PROC_END_DEPTNAME, b.PROC_END_TIME, b.
        ACT_DEF_UNIQUE_ID, b.ACT_DEF_UNIQUE_NAME, b.PROC_SEQ_NO, b.PROC_TYPE_ID, b.PROC_IS_SHOW, b.
        BID, b.MODELKEY,FORM_MAIN_NAME, b.PROC_TYPE, b.EXT1, b.EXT2, b.EXT3, b.EXT4, b.EXT5,t.TYPE_NAME from CSCP_PROC_BASE b left join cscp_proc_type t on b.PROC_TYPE_ID=t.type_id
        <where>
            <if test="obj!=null">
                <if test="obj.procInstId != null and obj.procInstId !=''">
                    and b.PROC_INST_ID = #{obj.procInstId,jdbcType=VARCHAR}
                </if>
                <if test="obj.procDefId != null and obj.procDefId !=''">
                    and  b.PROC_DEF_ID = #{obj.procDefId,jdbcType=VARCHAR}
                </if>
                <if test="obj.formDefId != null and obj.formDefId !=''">
                    and b.FORM_DEF_ID = #{obj.formDefId,jdbcType=VARCHAR}
                </if>
                <if test="obj.formDataId != null and obj.formDataId !=''">
                    and b.FORM_DATA_ID = #{obj.formDataId,jdbcType=VARCHAR}
                </if>
                <if test="obj.taskTitle != null and obj.taskTitle !=''">
                    and b.TASK_TITLE like concat('%',#{obj.taskTitle},'%')
                </if>
                <if test="obj.createTime != null and obj.createTime !=''">
                    and substr(b.CREATE_TIME,1,10)= #{obj.createTime,jdbcType=VARCHAR}
                </if>
                <if test="obj.procCreateId != null and obj.procCreateId!=''">
                    and b.PROC_CREATE_ID = #{obj.procCreateId,jdbcType=VARCHAR}
                </if>
                <if test="obj.procCreateName != null and obj.procCreateName !=''">
                    and b.PROC_CREATE_NAME like concat('%',#{obj.procCreateName},'%')
                </if>
                <if test="obj.procCreateDeptname != null and obj.procCreateDeptname !=''">
                    and  b.PROC_CREATE_DEPTNAME like concat('%',#{obj.procCreateDeptname},'%')
                </if>
                <if test="obj.procCreateDeptid != null and obj.procCreateDeptid !=''">
                    and b.PROC_CREATE_DEPTID = #{obj.procCreateDeptid,jdbcType=VARCHAR}
                </if>
                <if test="obj.procStatues != null and obj.procStatues !=''">
                    and b.PROC_STATUES = #{obj.procStatues,jdbcType=VARCHAR}
                </if>
                <if test="obj.procEndUserid != null and obj.procEndUserid !=''">
                    and b.PROC_END_USERID = #{obj.procEndUserid,jdbcType=VARCHAR}
                </if>
                <if test="obj.procEndUsername != null and obj.procEndUsername !=''">
                    and b.PROC_END_USERNAME = #{obj.procEndUsername,jdbcType=VARCHAR}
                </if>
                <if test="obj.procEndDeptid != null and obj.procEndDeptid !=''">
                    and b.PROC_END_DEPTID = #{obj.procEndDeptid,jdbcType=VARCHAR}
                </if>
                <if test="obj.procEndDeptname != null and obj.procEndDeptname !=''">
                    and b.PROC_END_DEPTNAME = #{obj.procEndDeptname,jdbcType=VARCHAR}
                </if>
                <if test="obj.procEndTime != null and obj.procEndTime !=''">
                    and b.PROC_END_TIME = #{obj.procEndTime,jdbcType=VARCHAR}
                </if>
                <if test="obj.actDefUniqueId != null and obj.actDefUniqueId !=''">
                    and  b.ACT_DEF_UNIQUE_ID = #{obj.actDefUniqueId,jdbcType=VARCHAR}
                </if>
                <if test="obj.actDefUniqueName != null and obj.actDefUniqueName !=''">
                    and b.ACT_DEF_UNIQUE_NAME = #{obj.actDefUniqueName,jdbcType=VARCHAR}
                </if>
                <if test="obj.procSeqNo != null and obj.procSeqNo !=''">
                    and  b.PROC_SEQ_NO = #{obj.procSeqNo,jdbcType=VARCHAR}
                </if>
                <if test="obj.procTypeId != null and obj.procTypeId !=''">
                    and b.PROC_TYPE_ID = #{obj.procTypeId,jdbcType=VARCHAR}
                </if>
                <if test="obj.procIsShow != null and obj.procIsShow !=''">
                    and b.PROC_IS_SHOW = #{obj.procIsShow,jdbcType=VARCHAR}
                </if>
                <if test="obj.bid != null and obj.bid !=''">
                    and b.BID = #{obj.bid,jdbcType=VARCHAR}
                </if>
                <if test="obj.modelkey != null and obj.modelkey !=''">
                    and  b.MODELKEY = #{obj.modelkey,jdbcType=VARCHAR}
                </if>
                <if test="obj.procType != null and obj.procType !=''">
                    and b.PROC_TYPE = #{obj.procType,jdbcType=VARCHAR}
                </if>
                <if test="obj.ext1 != null">
                    and b.EXT1 = #{obj.ext1,jdbcType=VARCHAR}
                </if>
                <if test="obj.ext2 != null">
                    and b.EXT2 = #{obj.ext2,jdbcType=VARCHAR}
                </if>
                <if test="obj.ext3 != null">
                    and  b.EXT3 = #{obj.ext3,jdbcType=VARCHAR}
                </if>
                <if test="obj.ext4 != null">
                    and b.EXT4 = #{obj.ext4,jdbcType=VARCHAR}
                </if>
                <if test="obj.ext5 != null">
                    and  b.EXT5 = #{obj.ext5,jdbcType=VARCHAR}
                </if>
                <if test="obj.variExt != null">
                    and  b.VARI_EXT = #{obj.variExt,jdbcType=LONGVARCHAR}
                </if>
            </if>
        </where>
        order by b.CREATE_TIME desc
    </select>

    <!-- 查询用户经办过的流程数据 -->
    <select id="getUserApproveProcessList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">
       select DISTINCT base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,
       base.create_time,base.create_name,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,
       base.ACT_DEF_UNIQUE_ID,ass.NODE_NAME  as ACT_DEF_UNIQUE_NAME,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time
        from cscp_proc_base base
       INNER JOIN cscp_audit_content content
            on base.PROC_INST_ID = content.PROC_INST_ID
        LEFT JOIN cscp_proc_assignee ass
        on base.PROC_INST_ID = ass.PROCESS_INSTANCE_ID

        <where>
        content.DELETED = 0
        <if test="obj.taskCandidateOrAssignedInStr != null and obj.taskCandidateOrAssignedInStr != ''">
            AND content.AUDITOR_ID = #{obj.taskCandidateOrAssignedInStr}
        </if>
        <if test="null != obj.taskCandidateOrAssignedIn and obj.taskCandidateOrAssignedIn.size > 0 ">
            AND content.AUDITOR_ID in
            <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="obj.bpmStatus != null and obj.bpmStatus != ''">
            AND base.bpm_status = #{obj.bpmStatus}
        </if>
        <if test="obj.typeId != null and obj.typeId != ''">
            AND base.FORM_DEF_ID = #{obj.typeId}
        </if>
        <if test="obj.title != null and obj.title != ''">
            AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
        </if>
        <if test="obj.auditorName != null and obj.auditorName != ''">
            AND content.AUDITOR_NAME like concat('%',#{obj.auditorName},'%')
        </if>

        <if test="obj.companyId != null and obj.companyId != ''">
            AND base.company_id  = #{obj.companyId}
        </if>

        <if test="obj.businessType != null and obj.businessType != ''">
            AND base.table_name  = #{obj.businessType}
        </if>


        <if test="obj.createName != null and obj.createName != ''">
            AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
        </if>


        <if test="obj.departmentName != null and obj.departmentName != ''">
            AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
        </if>


        <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
            AND base.create_time &gt;=   #{obj.processStartCreatTime}
        </if>


        <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
            AND base.create_time &lt;= #{obj.processEndCreatTime}
        </if>
        <if test="obj.isApproval != null and obj.isApproval != ''">
            <if test='obj.isApproval == "1".toString'>
                AND base.table_name  = 'approval'
            </if>
            <if test='obj.isApproval == "2".toString'>
                AND base.table_name  != 'approval'
            </if>
        </if>

        </where>

        order by base.create_time desc
    </select>

    <!-- 获取在办列表的数据 -->
    <select id="getHandleUserProcessList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">
        SELECT * FROM
        (
        SELECT ROW_NUMBER() OVER(PARTITION BY base.id ORDER BY content.create_time desc ) RN,
        content.create_time as  handleTime,
        base.id AS baseId,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,
        base.ACT_DEF_UNIQUE_ID,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time,pa.task_id,
        base.urgency,base.telegraph,pa.node_key as taskDefinitionKey
        from cscp_proc_base base
        INNER JOIN cscp_audit_content content
        on base.PROC_INST_ID = content.PROC_INST_ID
        left join
        cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID
        <where>
            content.DELETED = 0 and base.deleted = 0
            <if test="obj.taskCandidateOrAssignedInStr != null and obj.taskCandidateOrAssignedInStr != ''">
                AND content.AUDITOR_ID = #{obj.taskCandidateOrAssignedInStr}
            </if>
            <if test="null != obj.taskCandidateOrAssignedIn and obj.taskCandidateOrAssignedIn.size > 0 ">
                AND content.AUDITOR_ID in
                <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="obj.bpmStatus != null and obj.bpmStatus != ''">
                AND base.bpm_status in (2,3)
            </if>
            <if test="obj.typeId != null and obj.typeId != ''">
                AND base.FORM_DEF_ID = #{obj.typeId}
            </if>
            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND content.AUDITOR_NAME like concat('%',#{obj.auditorName},'%')
            </if>

            <if test="obj.companyId != null and obj.companyId != ''">
                AND base.company_id  = #{obj.companyId}
            </if>
            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>

            <if test="obj.processDefinitionKey != null and obj.processDefinitionKey != ''">
                AND base.MODELKEY  = #{obj.processDefinitionKey}
            </if>

            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>


            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>


            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>


            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>
            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
        </where>

        ORDER BY  base.bpm_status asc , content.create_time desc , base.create_time desc
    )
    where rn = 1
</select>

<!-- 获取我已办过的数据 -->
    <select id="getUserEndProcessList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">

        select DISTINCT base.id AS baseId,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,pa.create_time AS handleTime,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,pa.task_id,pa.ASSIGNEE_Name,pa.read_status,pa.ASSIGNEE,
        base.ACT_DEF_UNIQUE_ID,base.ACT_DEF_UNIQUE_NAME,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time
        from cscp_proc_base base
        INNER JOIN cscp_audit_content content
        on base.PROC_INST_ID = content.PROC_INST_ID
        left join
        cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID
        <where>
            content.DELETED = 0 and base.deleted = 0
            <if test="obj.taskCandidateOrAssignedInStr != null and obj.taskCandidateOrAssignedInStr != ''">
                AND content.AUDITOR_ID = #{obj.taskCandidateOrAssignedInStr}
            </if>
            <if test="null != obj.taskCandidateOrAssignedIn and obj.taskCandidateOrAssignedIn.size > 0 ">
                AND content.AUDITOR_ID in
                <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="obj.bpmStatus != null and obj.bpmStatus != ''">
                AND base.bpm_status = #{obj.bpmStatus}
            </if>
            <if test="obj.typeId != null and obj.typeId != ''">
                AND base.FORM_DEF_ID = #{obj.typeId}
            </if>
            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND content.AUDITOR_NAME like concat('%',#{obj.auditorName},'%')
            </if>

            <if test="obj.companyId != null and obj.companyId != ''">
                AND base.company_id  = #{obj.companyId}
            </if>

            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>

            <if test="obj.processDefinitionKey != null and obj.processDefinitionKey != ''">
                AND base.MODELKEY  = #{obj.processDefinitionKey}
            </if>

            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>
            <if test="obj.createId != null and obj.createId != ''">
                AND base.create_by  = #{obj.createId}
            </if>

            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>


            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>


            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>
            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
        </where>
        <choose>
            <when test="obj.bpmStatus != null and obj.bpmStatus ==3">
                order by base.PROC_END_TIME desc
            </when>
            <otherwise>
                <!--order by pa.create_time desc-->
                <!--按拟稿时间倒序排序-->
                order by base.create_time desc
            </otherwise>
        </choose>
    </select>

    <!-- 联络员获取领导用户已办结过的流程列表 -->
    <select id="getLiaisonUserEndProcessList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">
        SELECT title,
        create_time,
        bpm_status,
        document,
        annex,
        FORM_DATA_ID,
        FORM_DEF_ID,
        is_circularize,
        review_comments,
        handleTime,
        create_name,
        create_by,
        PROC_TYPE_ID,
        PROC_TYPE_NAME,
        PROC_DEF_ID,
        PROC_INST_ID,
        task_id,
        ASSIGNEE_Name,
        read_status,
        ASSIGNEE,
        ACT_DEF_UNIQUE_ID,
        NODE_NAME       AS ACT_DEF_UNIQUE_NAME,
        MODELKEY,
        department_name,
        root_PROC_INST_ID,
        PROC_END_TIME AS process_end_time from (
        select ROW_NUMBER() OVER(PARTITION BY base.id Order By content.create_time desc) as RN,
        content.create_time as handleTime ,
        base.id AS baseId,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,pa.task_id,pa.ASSIGNEE_Name,pa.read_status,pa.ASSIGNEE,
        base.ACT_DEF_UNIQUE_ID,pa.NODE_NAME,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME
        from cscp_proc_base base
        INNER JOIN cscp_audit_content content
        on base.PROC_INST_ID = content.PROC_INST_ID
        left join
        cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID
        <where>
            content.DELETED = 0
            <!--去除子流程意见-->
            AND base.root_PROC_INST_ID = base.PROC_INST_ID

            <if test="obj.taskCandidateOrAssignedInStr != null and obj.taskCandidateOrAssignedInStr != ''">
                AND content.AUDITOR_ID = #{obj.taskCandidateOrAssignedInStr}
            </if>

            <![CDATA[ and direct_secretary_general <> 1]]>

            <if test="null != obj.taskCandidateOrAssignedIn and obj.taskCandidateOrAssignedIn.size > 0 ">
                AND
                (
                    (content.AUDITOR_ID in
                    <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    AND base.direct_secretary_general = 0)
                    OR (
                        <if test="null != obj.directTaskCandidateOrAssignedIn and obj.directTaskCandidateOrAssignedIn.size > 0 ">
                            direct_secretary_general = 2
                            and content.AUDITOR_ID in
                            <foreach collection="obj.directTaskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                                #{id}
                            </foreach>
                        </if>
                        <if test="obj.directTaskCandidateOrAssignedIn == null or obj.directTaskCandidateOrAssignedIn.isEmpty()">
                             1 = 0
                        </if>
                    )
                )
            </if>

            <if test="null != obj.bpmStatusList and obj.bpmStatusList.size > 0 ">
                AND base.bpm_status in
                <foreach collection="obj.bpmStatusList" item="bpmStatus" index="index" open="(" close=")" separator=",">
                    #{bpmStatus}
                </foreach>
            </if>
            <if test="obj.bpmStatus != null and obj.bpmStatus != ''">
                AND base.bpm_status = #{obj.bpmStatus}
            </if>
            <if test="obj.typeId != null and obj.typeId != ''">
                AND base.FORM_DEF_ID = #{obj.typeId}
            </if>
            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND content.AUDITOR_NAME like concat('%',#{obj.auditorName},'%')
            </if>

            <if test="obj.companyId != null and obj.companyId != ''">
                AND base.company_id  = #{obj.companyId}
            </if>

            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>


            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>
            <if test="obj.createId != null and obj.createId != ''">
                AND base.create_by  = #{obj.createId}
            </if>

            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>


            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>


            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>

            <if test="obj.handleTimeStart != null and obj.handleTimeStart != ''">
                AND content.create_time &gt;=   #{obj.handleTimeStart}
            </if>


            <if test="obj.handleTimeEnd != null and obj.handleTimeEnd != ''">
                AND content.create_time &lt;= #{obj.handleTimeEnd}
            </if>



            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
        </where>
        <!--group by base.id-->
        ) as pa  where pa.RN=1
        order by pa.handleTime desc
    </select>

    <!-- 一键办结列表查询 -->
    <select id="getOneClickFinishProcessList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">
        select DISTINCT base.id AS baseId,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,pa.task_id,pa.ASSIGNEE_Name,pa.read_status,pa.ASSIGNEE,
        base.ACT_DEF_UNIQUE_ID,pa.NODE_NAME AS ACT_DEF_UNIQUE_NAME,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time
        from cscp_proc_base base
        INNER JOIN cscp_audit_content content
        on base.PROC_INST_ID = content.PROC_INST_ID
        left join
        cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID
        <where>
            content.DELETED = 0
            AND content.AUDIT_CONTENT = '将流程办结'
            <if test="obj.bpmStatus != null and obj.bpmStatus != ''">
                AND base.bpm_status = #{obj.bpmStatus}
            </if>
            <if test="obj.typeId != null and obj.typeId != ''">
                AND base.FORM_DEF_ID = #{obj.typeId}
            </if>
            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND content.AUDITOR_NAME like concat('%',#{obj.auditorName},'%')
            </if>

            <if test="obj.companyId != null and obj.companyId != ''">
                AND base.company_id  = #{obj.companyId}
            </if>

            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>

            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>

            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>

            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>

            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>
            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
        </where>
        <choose>
            <when test="obj.bpmStatus != null and obj.bpmStatus ==3">
                order by base.PROC_END_TIME desc
            </when>
            <otherwise>
                order by base.create_time desc
            </otherwise>
        </choose>
    </select>


    <!-- 查询监控的流程的  -->
    <select id="getProcessMonitorList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">

        select DISTINCT base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,
      base.MODELKEY,base.department_name, ass.NODE_KEY as ACT_DEF_UNIQUE_ID ,base.root_PROC_INST_ID,
      ass.NODE_NAME  as ACT_DEF_UNIQUE_NAME,ass.ASSIGNEE,ass.ASSIGNEE_Name,ass.task_id, base.id AS baseId
        from cscp_proc_base base
        INNER JOIN cscp_proc_assignee ass
        on base.PROC_INST_ID = ass.PROCESS_INSTANCE_ID

        where base.deleted = 0

        <if test="obj.taskCandidateOrAssignedInStr != null and obj.taskCandidateOrAssignedInStr != ''">
            AND base.create_by = #{obj.taskCandidateOrAssignedInStr}
        </if>
        <if test="obj.bpmStatus != null and obj.bpmStatus != ''">
            AND base.bpm_status = #{obj.bpmStatus}
        </if>

        <if test="obj.bpmStatus == null or obj.bpmStatus == '' ">
            AND base.bpm_status in (2,4)
        </if>

        <if test="obj.typeId != null and obj.typeId != ''">
            AND base.FORM_DEF_ID = #{obj.typeId}
        </if>
        <if test="obj.title != null and obj.title != ''">
            AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
        </if>

        <if test="obj.companyId != null and obj.companyId != ''">
            AND base.company_id  = #{obj.companyId}
        </if>

        <if test="obj.businessType != null and obj.businessType != ''">
            AND base.table_name  = #{obj.businessType}
        </if>


        <if test="obj.createName != null and obj.createName != ''">
            AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
        </if>


        <if test="obj.departmentName != null and obj.departmentName != ''">
            AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
        </if>

        <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
            AND base.create_time  &gt;=  #{obj.processStartCreatTime}
        </if>


        <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
            AND base.create_time &lt;= #{obj.processEndCreatTime}
        </if>
        <if test="obj.assigneeName != null and obj.assigneeName != ''">
            AND ass.ASSIGNEE_Name = #{obj.assigneeName}
        </if>

        order by base.create_time desc
    </select>

    <!-- 查询监控的流程的,按单位过滤  -->
    <select id="getCompanyMonitorProcessList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">

        select DISTINCT base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,
        base.create_time,base.create_name,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,
        base.MODELKEY,base.department_name, ass.NODE_KEY as ACT_DEF_UNIQUE_ID ,base.root_PROC_INST_ID,
        ass.NODE_NAME  as ACT_DEF_UNIQUE_NAME,ass.ASSIGNEE,ass.ASSIGNEE_Name,ass.task_id
        from cscp_proc_base base
        INNER JOIN cscp_proc_assignee ass
        on base.PROC_INST_ID = ass.PROCESS_INSTANCE_ID
        INNER JOIN cscp_audit_content content
        on base.PROC_INST_ID = content.PROC_INST_ID
        where base.deleted = 0 and content.DELETED = 0

        <if test="obj.taskCandidateOrAssignedInStr != null and obj.taskCandidateOrAssignedInStr != ''">
            AND base.create_by = #{obj.taskCandidateOrAssignedInStr}
        </if>

        <if test="obj.typeId != null and obj.typeId != ''">
            AND base.FORM_DEF_ID = #{obj.typeId}
        </if>
        <if test="obj.title != null and obj.title != ''">
            AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
        </if>
        <if test="obj.auditorName != null and obj.auditorName != ''">
            AND content.AUDITOR_NAME like concat('%',#{obj.auditorName},'%')
        </if>
        <if test="obj.companyId != null and obj.companyId != ''">
            AND base.company_id  = #{obj.companyId}
        </if>

        <if test="obj.businessType != null and obj.businessType != ''">
            AND base.table_name  = #{obj.businessType}
        </if>


        <if test="obj.createName != null and obj.createName != ''">
            AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
        </if>


        <if test="obj.departmentName != null and obj.departmentName != ''">
            AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
        </if>

        <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
            AND base.create_time  &gt;=  #{obj.processStartCreatTime}
        </if>


        <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
            AND base.create_time &lt;= #{obj.processEndCreatTime}
        </if>

        order by base.create_time desc
    </select>

    <update id="updateIsCircularizeByProcInstId">
        update cscp_proc_base set is_circularize = 1,review_comments = #{reviewComments} where PROC_INST_ID = #{procInstId}
    </update>

    <update id="changePrintDownloadStatus">
        update biz_approval_management
            set is_print = #{isPrint},is_print_name = #{isPrintName}
        where
            process_instance_id = #{processInstanceId}
            and create_by =#{userId}

    </update>

    <select id="getLiaisonActivelyTaskListUnionOutAuth" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">
        select DISTINCT RES.ID_ AS task_id,RES.NAME_ AS act_def_unique_name,RES.TASK_DEF_KEY_ AS taskDefinitionKey,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,pa.ASSIGNEE_Name,pa.read_status,pa.ASSIGNEE,
        base.ACT_DEF_UNIQUE_ID,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time,RES.CREATE_TIME_
        from ACT_RU_TASK RES
        INNER JOIN cscp_proc_base base
        on RES.PROC_INST_ID_ = base.PROC_INST_ID
        INNER JOIN cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID and RES.ID_ = pa.task_id
        <where>
            base.DELETED = 0
            and RES.SUSPENSION_STATE_ = 1
            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND pa.ASSIGNEE_Name like concat('%',#{obj.auditorName},'%')
            </if>

            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>

            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>
            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>

            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>

            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>
            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
            AND (
                (RES.ASSIGNEE_ in
                <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
                AND pa.ASSIGNEE in
                <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")"
                         separator=",">
                    #{id}
                </foreach>
                AND base.direct_secretary_general = 0
                )
                OR
                (
                    <if test="obj.directTaskCandidateOrAssignedIn != null and obj.directTaskCandidateOrAssignedIn.size() > 0">
                        RES.ASSIGNEE_ in
                        <foreach collection="obj.directTaskCandidateOrAssignedIn" item="id" index="index" open="(" close=")"
                                 separator=",">
                            #{id}
                        </foreach>
                        AND pa.ASSIGNEE in
                        <foreach collection="obj.directTaskCandidateOrAssignedIn" item="id" index="index" open="(" close=")"
                                 separator=",">
                            #{id}
                        </foreach>
                        AND base.direct_secretary_general = 2
                    </if>
                    <if test="obj.directTaskCandidateOrAssignedIn == null or obj.directTaskCandidateOrAssignedIn.isEmpty()">
                        1 = 0
                    </if>
                )
            )
            <![CDATA[ and direct_secretary_general <> 1]]>
        </where>
        order by RES.CREATE_TIME_ desc
    </select>

    <!-- 获取书记的待办列表 -->
    <select id="getSecretaryActivelyTaskListUnionOutAuth" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">
        select DISTINCT RES.ID_ AS task_id,RES.NAME_ AS act_def_unique_name,RES.TASK_DEF_KEY_ AS taskDefinitionKey,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,pa.ASSIGNEE_Name,pa.read_status,pa.ASSIGNEE,
        base.ACT_DEF_UNIQUE_ID,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time
        from ACT_RU_TASK RES
        INNER JOIN cscp_proc_base base
        on RES.PROC_INST_ID_ = base.PROC_INST_ID
        INNER JOIN cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID and RES.ID_ = pa.task_id
        left JOIN cscp_document_file cdf
        on base.FORM_DATA_ID = cdf.FORM_DATA_ID
        <where>
            base.DELETED = 0
            and RES.SUSPENSION_STATE_ = 1
            and cdf.DELETED = 0
            and (cdf.all_pdf_path is not null and cdf.all_pdf_path != '')
            AND RES.ASSIGNEE_ in
            <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND pa.ASSIGNEE_Name like concat('%',#{obj.auditorName},'%')
            </if>

            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>

            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>
            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>

            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>

            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>
            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
        </where>
        order by RES.CREATE_TIME_ desc
    </select>

    <select id="getLiaisonActivelyTaskListUnionOutAuthSubscript" resultType="com.ctsi.activiti.core.vo.TaskVO" resultMap="taskVOResultMap">
        select DISTINCT RES.ID_ AS task_id,RES.NAME_ AS act_def_unique_name,RES.TASK_DEF_KEY_ AS taskDefinitionKey,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,pa.ASSIGNEE_Name,pa.read_status,pa.ASSIGNEE,
        base.ACT_DEF_UNIQUE_ID,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time,RES.CREATE_TIME_
        from ACT_RU_TASK RES
        INNER JOIN cscp_proc_base base
        on RES.PROC_INST_ID_ = base.PROC_INST_ID
        INNER JOIN cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID and RES.ID_ = pa.task_id
        <where>
            base.DELETED = 0
            and RES.SUSPENSION_STATE_ = 1
            AND RES.ASSIGNEE_ in
            <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
            AND pa.ASSIGNEE in
            <foreach collection="obj.taskCandidateOrAssignedIn" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>

            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND pa.ASSIGNEE_Name like concat('%',#{obj.auditorName},'%')
            </if>

            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>

            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>
            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>

            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>

            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>
            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
        </where>
        order by RES.CREATE_TIME_ desc
    </select>

    <!-- 获取待办在办已办三种状态列表的数据 -->
    <select id="getUserProcessList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultMap="taskVOResultMap">
        select DISTINCT base.id AS baseId,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
        base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,
        base.ACT_DEF_UNIQUE_ID,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time
        from cscp_proc_base base
        INNER JOIN cscp_audit_content content
        on base.PROC_INST_ID = content.PROC_INST_ID
        left join
        cscp_proc_assignee pa
        on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID
        <where>
            base.deleted = 0
            AND (content.AUDITOR_ID = #{obj.taskCandidateOrAssignedInStr} or pa.ASSIGNEE = #{obj.taskCandidateOrAssignedInStr})
            AND base.bpm_status in (2,3)
            <if test="obj.title != null and obj.title != ''">
                AND base.title like   CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
            <if test="obj.businessType != null and obj.businessType != ''">
                AND base.table_name  = #{obj.businessType}
            </if>
            <if test="obj.processDefinitionKey != null and obj.processDefinitionKey != ''">
                AND base.MODELKEY  = #{obj.processDefinitionKey}
            </if>

            <if test="obj.createName != null and obj.createName != ''">
                AND base.create_name like   CONCAT('%',CONCAT(#{obj.createName},'%'))
            </if>
            <if test="obj.departmentName != null and obj.departmentName != ''">
                AND base.department_name like   CONCAT('%',CONCAT(#{obj.departmentName},'%'))
            </if>
            <if test="obj.processStartCreatTime != null and obj.processStartCreatTime != ''">
                AND base.create_time &gt;=   #{obj.processStartCreatTime}
            </if>
            <if test="obj.processEndCreatTime != null and obj.processEndCreatTime != ''">
                AND base.create_time &lt;= #{obj.processEndCreatTime}
            </if>
            <if test="obj.isApproval != null and obj.isApproval != ''">
                <if test='obj.isApproval == "1".toString'>
                    AND base.table_name  = 'approval'
                </if>
                <if test='obj.isApproval == "2".toString'>
                    AND base.table_name  != 'approval'
                </if>
            </if>
        </where>
        order by base.create_time desc
    </select>



    <select id="getMyAllBusinessCscpProcPage" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultType="java.util.HashMap" >
select * from (
  select   COALESCE(apm.receive_document_type_name, name_) AS receiveTypeName, s.*,apm.document_no  AS  documentNo,apm.duration_classification_name AS durationClassificationName  from (
        select '1' as bpm_status, '1' as procStatus,cpr.name_,b.FORM_DATA_ID formDataId,'' as procTypeName,
        b.process_definition_id as fromDefId,'' AS rootProcessInstanceId,
        id as formTemporaryStorageId,
        b.process_definition_key   as processDefinitionKey,
        b.process_definition_id   as processDefinitionId,
        b.form_id   as formId,
        '' AS cscpProcBaseId,
        '' AS procInstId,
        '' AS processInstanceId,
        '' AS procEndTime,
       '起草'   as actDefUniqueName,
        b.CREATE_BY as ASSIGNEE, b.create_name as assignessName,
       '' as taskId,b.create_time,b.create_name createName,b.title,b.create_by createBy,'' urgency,'' telegraph
       from biz_form_temporary_storage b
        LEFT JOIN  cscp_proc cpr ON b.process_definition_id = cpr.PROCESS_DEFINITION_ID_
        where b.deleted = 0 and b.create_by= #{obj.createId}
        <if test="obj.title != null and obj.title != ''">
            AND b.title like CONCAT('%',CONCAT(#{obj.title},'%'))
            and b.title is not null
        </if>

       union  all

        select DISTINCT b.bpm_status bpm_status,b.bpm_status AS procStatus,cpr.name_,b.FORM_DATA_ID formDataId,b.PROC_TYPE_NAME procTypeName,
        b.FORM_DEF_ID formDefId,b.root_PROC_INST_ID AS rootProcessInstanceId,
        '' as formTemporaryStorageId,
        b.MODELKEY   as processDefinitionKey,
        b.PROC_DEF_ID   as processDefinitionId,
        b.FORM_DEF_ID   as formId,

        b.id AS cscpProcBaseId,
        b.PROC_INST_ID AS procInstId,
        b.PROC_INST_ID AS processInstanceId,
        b.PROC_END_TIME AS procEndTime,
       ass.NODE_NAME   as actDefUniqueName,
       ass.ASSIGNEE,ass.ASSIGNEE_Name assignessName,
       ass.task_id taskId,b.create_time,b.create_name createName,b.title,b.create_by createBy, b.urgency,b.telegraph
        from   cscp_proc_base b
        left join cscp_audit_content content on b.PROC_INST_ID = content.PROC_INST_ID
       left join ( select qq.*,ROW_NUMBER() OVER(PARTITION BY qq.PROCESS_INSTANCE_ID ORDER BY qq.id desc ) rn from
        cscp_proc_assignee qq
        ) ass on b.PROC_INST_ID = ass.PROCESS_INSTANCE_ID and rn=1
        LEFT JOIN  cscp_proc cpr ON b.FORM_DEF_ID = cpr.PROCESS_DEFINITION_ID_
        where b.deleted = 0 and b.create_by= #{obj.createId} and b.PROC_INST_ID = b.root_PROC_INST_ID
        <if test="obj.title != null and obj.title != ''">
            AND b.title like CONCAT('%',CONCAT(#{obj.title},'%'))
            and b.title is not null
        </if>
            ) s
            LEFT JOIN biz_approval_management apm ON apm.id = s.formDataId
        ) st
        <where>
            <if test="obj.documentNo != null and obj.documentNo != ''">
                st.documentNo LIKE CONCAT('%',CONCAT(#{obj.documentNo},'%'))
            </if>
            <if test="obj.receiveTypeName != null and obj.receiveTypeName != ''">
                <!--模糊查询 receiveTypeName-->
                AND st.receiveTypeName LIKE CONCAT('%',CONCAT(#{obj.receiveTypeName},'%'))
            </if>
        </where>

        order by st.create_time desc


    </select>
    <select id="getApproveManagementCode" resultType="java.lang.String">
        select max(CAST(documentNo AS NUMBER(18))) managementCode
        from (select substring(document_no, locate('〕', document_no) + 1,
                               locate('号', document_no) - locate('〕', document_no) - 1) documentNo
              from biz_approval_management
              where department_id = #{departmentId}
                and deleted = 0
                and create_time between #{startTime} and #{endTime}
                <choose>
                    <when test="receiveType != null and receiveType != ''">
                        and receive_document_type = #{receiveType}
                    </when>
                    <otherwise>
                        and receive_document_type is null
                    </otherwise>
                </choose>
                and document_no is not null and substring(document_no, locate('〕', document_no) + 1,
        locate('号', document_no) - locate('〕', document_no) - 1) is not null and bpm_status > 1)
    </select>
    <select id="getApproveInnerCode" resultType="java.lang.String">
        select max(CAST(innerDocNumber AS NUMBER(18))) innerDocCode
        from (select substring(inner_doc_number, locate('〕', inner_doc_number) + 1,
                               locate('号', inner_doc_number) - locate('〕', inner_doc_number) - 1) innerDocNumber
              from biz_approval_management apm
                left join cscp_proc_base base on base.BID = apm.id
                where apm.department_id = #{departmentId}
                and apm.deleted = 0
                and apm.create_time between #{startTime} and #{endTime}
                <!--<choose>
                    <when test="receiveType != null and receiveType != ''">
                        and apm.receive_document_type = #{receiveType}
                    </when>
                    <otherwise>
                        and apm.receive_document_type is null
                    </otherwise>
                </choose>-->
                <if test="formId != null and formId != ''">
                    AND base.FORM_DEF_ID = #{formId}
                </if>
                and inner_doc_number is not null and substring(inner_doc_number, locate('〕', inner_doc_number) + 1,
        locate('号', inner_doc_number) - locate('〕', inner_doc_number) - 1) is not null and apm.bpm_status > 1)
    </select>

    <select id="getMaxLastThreeApproveManagementCode" resultType="java.lang.String">
        select max(documentNo) managementCode
        from (select substring(apm.document_no, locate('〕', apm.document_no) + 2,
        locate('号', apm.document_no) - locate('〕', apm.document_no) - 2) documentNo
        from biz_approval_management apm
        left join cscp_proc_base base on base.BID = apm.id
        where apm.department_id = #{departmentId}
        and apm.deleted = 0
        and apm.create_time between #{startTime} and #{endTime}
        <choose>
            <when test="receiveType != null and receiveType != ''">
                and apm.receive_document_type = #{receiveType}
            </when>
            <otherwise>
                and apm.receive_document_type is null
            </otherwise>
        </choose>
        <if test="formId != null and formId != ''">
            AND base.FORM_DEF_ID = #{formId}
        </if>
        and apm.document_no is not null and substring(apm.document_no, locate('〕', apm.document_no) + 2,
        locate('号', apm.document_no) - locate('〕', apm.document_no) - 2) is not null and apm.bpm_status > 1)
    </select>

    <select id="queryAllProcessMonitorList" resultType="com.ctsi.business.dto.QueryAllProcessMonitorDTO">
        select DISTINCT base.title,
        base.bpm_status,
        base.document,
        base.annex,
        base.FORM_DATA_ID,
        base.FORM_DEF_ID formId,
        base.create_time processCreatTime,
        base.create_name,
        base.create_by,
        base.PROC_TYPE_NAME,
        base.PROC_DEF_ID processDefinitionId,
        base.PROC_INST_ID processInstanceId,
        base.MODELKEY processDefinitionKey,
        base.company_name,
        base.department_name,
        ass.NODE_KEY  as ACT_DEF_UNIQUE_ID,
        base.root_PROC_INST_ID rootProcessInstanceId,
        ass.NODE_NAME as name,
        ass.ASSIGNEE assingee,
        ass.ASSIGNEE_Name,
        ass.task_id,
        base.id       AS baseId,
        ass.create_time sendTime
        from cscp_proc_base base
        INNER JOIN cscp_proc_assignee ass

        on base.PROC_INST_ID = ass.PROCESS_INSTANCE_ID

        where base.deleted = 0 AND base.bpm_status = 2

        <if test="vo.departmentName != null and vo.departmentName != ''">
            AND base.department_name like   CONCAT('%',CONCAT(#{vo.departmentName},'%'))
        </if>

        <if test="vo.processStartCreatTime != null and vo.processStartCreatTime != ''">
            AND ass.create_time  &gt;=  #{vo.processStartCreatTime}
        </if>

        <if test="vo.processEndCreatTime != null and vo.processEndCreatTime != ''">
            AND ass.create_time &lt;= #{vo.processEndCreatTime}
        </if>
        <if test="vo.assigneeName != null and vo.assigneeName != ''">
            AND ass.ASSIGNEE_Name = #{vo.assigneeName}
        </if>

        order by ass.create_time desc
    </select>

    <select id="querySmsNoticeList" resultType="com.ctsi.business.dto.QuerySmsNoticeListDTO">
        select TASK_ID,count(TASK_ID) noticeCount
        from
            SW_SMS_NOTIFICATION_RECORDS
        where
            TASK_ID  in
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        group by task_id
    </select>




    <select id="getProcessInfoByInstanceId"  resultMap="taskVOResultMap">
        SELECT * FROM
            (
                SELECT ROW_NUMBER() OVER(PARTITION BY base.id ORDER BY pa.create_time desc ) RN,
                       base.id AS baseId,base.title,base.bpm_status,base.document,base.annex,base.FORM_DATA_ID,base.FORM_DEF_ID,base.is_circularize,base.review_comments,
                       base.create_time,base.create_name,base.create_by,base.PROC_TYPE_ID,base.PROC_TYPE_NAME,base.PROC_DEF_ID,base.PROC_INST_ID,
                       base.ACT_DEF_UNIQUE_ID,base.MODELKEY,base.department_name,base.root_PROC_INST_ID,base.PROC_END_TIME as process_end_time,pa.task_id,
                       pa.NODE_KEY AS nodekey,
                       pa.NODE_Name AS nodeName,
                       pa.ASSIGNEE_Name,
                       base.urgency,base.telegraph
                from cscp_proc_base base
                         left join
                     cscp_proc_assignee pa
                     on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID

                where  base.deleted = 0   and    base.PROC_INST_ID =  #{instanceId}
                ORDER BY  base.bpm_status asc ,base.create_time desc
            )
        where rn = 1
    </select>
    <select id="queryApproveManagementCode" resultType="com.ctsi.business.dto.QueryApproveManagementCodeDTO">
        select ID approvalManagementId,
               document_no,
               substring(change_document_no, locate('〕', change_document_no) + 1,
                                                 locate('号', change_document_no) - locate('〕', change_document_no) - 1) changeDocumentNo
        from biz_approval_management
        where department_id = #{departmentId}
          and deleted = 0
          and create_time >= '2024-01-01 00:00:00'
          and receive_document_type = #{type}
          and change_document_no is not null
          and bpm_status > 1
        order by changeDocumentNo
    </select>

    <!--已办件动态-->
    <select id="queryDoneApprovalList" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultType="com.ctsi.activiti.core.vo.TaskContentVO">
        SELECT baseId,title,AUDIT_CONTENT,AUDITOR_NAME,AUDIT_TIME,bpm_status,process_definition_key,
                process_instance_id, root_process_instance_id,task_id,NODE_KEY,PROC_TYPE_NAME, form_data_id, form_id
        FROM (
            select
            ROW_NUMBER() OVER (PARTITION BY title ORDER BY AUDIT_TIME DESC) AS rn,
            MAX(AUDIT_TIME) OVER (PARTITION BY title) AS time_desc,
            base.id AS baseId,base.title,content.AUDIT_CONTENT,content.AUDITOR_NAME,content.AUDIT_TIME,base.bpm_status,
            base.MODELKEY AS process_definition_key, base.PROC_INST_ID AS process_instance_id, base.root_PROC_INST_ID AS root_process_instance_id,
            content.task_id,pa.NODE_KEY,base.PROC_TYPE_NAME, base.FORM_DATA_ID AS form_data_id, base.FORM_DEF_ID AS form_id
            from cscp_proc_base base
            INNER JOIN cscp_audit_content content on base.PROC_INST_ID = content.PROC_INST_ID
            left join (SELECT * from (SELECT ROW_NUMBER() OVER(PARTITION BY PROCESS_INSTANCE_ID Order By Id) as RN,PROCESS_INSTANCE_ID, NODE_KEY, TASK_ID FROM cscp_proc_assignee) where RN=1) AS pa
               on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID
            where
            content.DELETED = 0 and base.deleted = 0
            and AUDIT_CONTENT is not null
            <!--意见栏的表单id不为空的，为表单意见-->
            and COMMENTS_FORM_ID is not null
            <if test="obj.title != null and obj.title != ''">
                AND base.title LIKE concat('%',#{obj.title},'%')
            </if>
            <if test="obj.auditorName != null and obj.auditorName != ''">
                AND content.AUDITOR_NAME concat('%',#{obj.auditorName},'%')
            </if>
            <if test="obj.taskCandidateOrAssignedInStr != null and obj.taskCandidateOrAssignedInStr != ''">
                and base.PROC_INST_ID in (select PROC_INST_ID from cscp_audit_content where AUDITOR_ID=#{obj.taskCandidateOrAssignedInStr})
            </if>
            <if test="obj.processDefinitionKeyIn !=null and obj.processDefinitionKeyIn.size>0">
                AND base.PROC_INST_ID IN
                <foreach collection="obj.processDefinitionKeyIn" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

        ) AS subquery
        ORDER BY time_desc DESC
        <!--  ORDER BY content.AUDIT_TIME DESC, base.id DESC-->
    </select>

    <!--三服务-查询当前用户待办呈批件数量-->
    <select id="countHandlerUserApproval" resultType="java.lang.Long">
        select count(distinct RES.ID_)
        FROM ACT_RU_TASK RES
        LEFT JOIN ACT_RU_IDENTITYLINK I
        ON I.TASK_ID_ = RES.ID_
        WHERE RES.SUSPENSION_STATE_ = 1
        <if test="startTime != null">
            AND CREATE_TIME_ &gt;=  #{startTime}
        </if>
        <if test="endTime != null">
            AND CREATE_TIME_ &lt;= #{endTime}
        </if>
        and (RES.ASSIGNEE_ = #{userId} or
        (RES.ASSIGNEE_ is null and I.TYPE_ = 'candidate' and (I.USER_ID_ = #{userId})));
    </select>

    <!--三服务-查询当前用户待阅信息传阅数量-->
    <select id="countHandlerUserCirclate" resultType="java.lang.Long">
        SELECT
            count(ID)
        FROM t_circulate_user
        WHERE
            deleted = 0
            AND create_by = #{userId}
            AND FIRST_OPEN_TIME is null
        <if test="startTime != null">
            AND CREATE_TIME &gt;=  #{startTime}
        </if>
        <if test="endTime != null">
            AND CREATE_TIME &lt;= #{endTime}
        </if>
    </select>

    <!--查询是否存在转办子呈批件，子呈批件是否存在-->
    <select id="selectForwardProcBase" resultType="com.ctsi.business.domain.CscpProcBase">
        SELECT b.*
        FROM cscp_proc_base b
        LEFT JOIN biz_approval_forward_record r ON b.proc_inst_id = r.process_instance_id
        WHERE r.process_instance_id IS NOT NULL
        and b.deleted=0
        and r.deleted=0
        <!--processInstanceId为父呈批件实例id-->
        AND r.forward_process_instance_id = #{forwardProcessInstanceId}
        <!--bpm_status 流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废-->
        AND b.bpm_status NOT IN (3, 5);

    </select>


    <select id="checkOfficialDocumentNumber" parameterType="com.ctsi.business.vo.QueryApproveManagementCodeVO" resultType="java.lang.String">
        select
            CASE
                WHEN #{vo.codeType} = 0 THEN apm.inner_doc_number
                ELSE apm.document_no
            END document_no
        from biz_approval_management apm
        left join cscp_proc_base base on base.BID = apm.id
        where
            apm.department_id = #{vo.deptId}
            and apm.deleted = 0
            and apm.create_time between #{startTime} and #{endTime}
            <if test="vo.approvalCode != null and vo.approvalCode != ''">
                AND apm.document_no = #{vo.approvalCode}
            </if>
            <if test="vo.innerDocCode != null and vo.innerDocCode != ''">
                AND apm.inner_doc_number = #{vo.innerDocCode}
            </if>
            <if test="vo.formId != null and vo.formId != ''">
                AND base.FORM_DEF_ID = #{vo.formId}
            </if>
            <if test="vo.receiveType != null and vo.receiveType != ''">
                and receive_document_type = #{vo.receiveType}
            </if>
            <!--外部发文文号-->
            <if test="vo.codeType ==1">
                and apm.document_no is not null
            </if>
            <!--内部文号-->
            <if test="vo.codeType ==0">
                and apm.inner_doc_number is not null
            </if>

        and apm.bpm_status > 1
        order by apm.create_time desc limit 10;

    </select>

    <!--获取收文呈批件，指定收文类型历史最大文号-->
    <select id="selectRecApprovalMaxCode" resultType="java.lang.String">
        select CAST(REGEXP_REPLACE(document_no, '[^0-9]+', '') AS NUMBER(18)) as doNo from (
            SELECT
                    substring(apm.document_no, locate('〕', apm.document_no) + 2,
                    locate('号', apm.document_no) - locate('〕', apm.document_no) - 2) documentNo
            from biz_approval_management apm
            left join cscp_proc_base base on base.BID = apm.id
            where
                apm.document_no is not null
                and apm.bpm_status > 1
                and apm.department_id = #{vo.deptId}
                and apm.deleted = 0
                and apm.create_time between #{startTime} and #{endTime}
                <if test="vo.formId != null and vo.formId != ''">
                    AND base.FORM_DEF_ID = #{vo.formId}
                </if>
        ) where
            doNo is not null
        order by doNo desc
        limit 1;
    </select>
    <select id="selectByExaminationId" resultType="java.util.Map">
        select * from t_regulations_examination where id=#{examinationId}
    </select>

    <update id="updateCpbIdById">
        update t_regulations_examination set CPB_ID=#{cpbId} where id=#{id}
    </update>

    <update id="updateFormDataId">
        update t_law_file_examine_print set form_data_id = #{newFormDateId},id = #{newFormDateId} where form_data_id = #{oldFormDateId}
    </update>

    <update id="updateSbbaFormDataId">
        update t_law_file_report_high set form_data_id = #{newFormDateId},id = #{newFormDateId} where form_data_id = #{oldFormDateId}
    </update>


    <update id="saveProcessInstanceId">
        update t_law_file_examine_print set PROCESS_INSTANCE_ID = #{processInstanceId} where form_data_id = #{formDataId}
    </update>

    <update id="saveSbbaProcessInstanceId">
        update t_law_file_report_high set PROCESS_INSTANCE_ID = #{processInstanceId} where form_data_id = #{formDataId}
    </update>

</mapper>



