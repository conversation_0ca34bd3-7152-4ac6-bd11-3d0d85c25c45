<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpAuditContentRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpAuditContent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 25 15:02:49 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
    <result column="PROC_INST_ID" jdbcType="VARCHAR" property="procInstId" />
    <result column="ACT_ID" jdbcType="VARCHAR" property="actId" />
    <result column="ACT_NAME" jdbcType="VARCHAR" property="actName" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="AUDITOR_ID" jdbcType="VARCHAR" property="auditorId" />
    <result column="AUDITOR_NAME" jdbcType="VARCHAR" property="auditorName" />
    <result column="AUDIT_TIME" jdbcType="VARCHAR" property="auditTime" />
    <result column="DELEGATOR_ID" jdbcType="VARCHAR" property="delegatorId" />
    <result column="DELEGATOR_NAME" jdbcType="VARCHAR" property="delegatorName" />
    <result column="AUDIT_CONTENT" jdbcType="VARCHAR" property="auditContent" />
    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
    <result column="PROCESS_STATUS" jdbcType="VARCHAR" property="processStatus" />
    <result column="CLIENT_TYPE" jdbcType="VARCHAR" property="clientType" />
    <result column="EXT1" jdbcType="VARCHAR" property="ext1" />
    <result column="EXT2" jdbcType="VARCHAR" property="ext2" />
    <result column="EXT3" jdbcType="VARCHAR" property="ext3" />
    <result column="EXT4" jdbcType="VARCHAR" property="ext4" />
    <result column="EXT5" jdbcType="VARCHAR" property="ext5" />
    <result column="COMMENTS_FORM_ID" jdbcType="VARCHAR" property="commentsFormId" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 25 15:02:49 CST 2019.
    -->
    ID, PROC_DEF_ID, PROC_INST_ID, ACT_ID, ACT_NAME, TITLE, AUDITOR_ID, AUDITOR_NAME, 
    AUDIT_TIME, DELEGATOR_ID, DELEGATOR_NAME, AUDIT_CONTENT, AUDIT_STATUS, PROCESS_STATUS, 
    CLIENT_TYPE, EXT1, EXT2, EXT3, EXT4, EXT5 , COMMENTS_FORM_ID
  </sql>
  <update id="deleteByprocessInstanceId">
    update cscp_audit_content set DELETED = 1 where PROC_INST_ID = #{processInstanceId}
  </update>
</mapper>