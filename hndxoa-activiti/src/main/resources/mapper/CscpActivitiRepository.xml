<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpActivitRepository">
    <resultMap id="ModelBaseResultMap" type="com.ctsi.activiti.bpmn.entity.CscpProc">
        <id column="ID_"  property="id"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEFINITION_KEY_"  property="processDefinitionKey"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEFINITION_ID_"  property="processDefinitionId"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEPLOYMENT_ID_"  property="processDeploymentId"   jdbcType="VARCHAR" />
        <result column="NAME_"  property="name"   jdbcType="VARCHAR" />
        <result column="MAX_VERSION_"  property="maxVersion"  jdbcType="VARCHAR" />
        <result column="DEL"  property="del"  jdbcType="VARCHAR" />
        <result column="CREATE_TIME"  property="createTime"  jdbcType="VARCHAR" />
        <result column="UPDATE_TIME"  property="updateTime"  jdbcType="VARCHAR" />
        <result column="TYPE_ID"  property="typeId"   jdbcType="VARCHAR" />
        <result column="FORM_TYPE"  property="formType"   jdbcType="VARCHAR" />
        <result column="OPERATE_TYPE"  property="operateType"   jdbcType="VARCHAR"/>
        <result column="SEQ_ID" property="seqId" jdbcType="VARCHAR" />
        <result column="FORM_ID" property="formId" jdbcType="VARCHAR" />
        <result column="FORM_NAME" property="formName" jdbcType="VARCHAR" />
        <result column="is_permission" property="isPermission" jdbcType="TINYINT" />
        <result column="permission_type" property="permissionType" jdbcType="VARCHAR" />
        <result column="permission_type_values" property="permissionTypeValues" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="ModelBaseResultTenantIdMap" type="com.ctsi.activiti.bpmn.dto.CscpProcDTO">
        <id column="ID_"  property="id"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEFINITION_KEY_"  property="processDefinitionKey"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEFINITION_ID_"  property="processDefinitionId"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEPLOYMENT_ID_"  property="processDeploymentId"   jdbcType="VARCHAR" />
        <result column="NAME_"  property="name"   jdbcType="VARCHAR" />
        <result column="MAX_VERSION_"  property="maxVersion"  jdbcType="VARCHAR" />
        <result column="DEL"  property="del"  jdbcType="VARCHAR" />
        <result column="CREATE_TIME"  property="createTime"  jdbcType="VARCHAR" />
        <result column="UPDATE_TIME"  property="updateTime"  jdbcType="VARCHAR" />
        <result column="TYPE_ID"  property="typeId"   jdbcType="VARCHAR" />
        <result column="FORM_TYPE"  property="formType"   jdbcType="VARCHAR" />
        <result column="OPERATE_TYPE"  property="operateType"   jdbcType="VARCHAR"/>
        <result column="SEQ_ID" property="seqId" jdbcType="VARCHAR" />
        <result column="FORM_ID" property="formId" jdbcType="VARCHAR" />
        <result column="FORM_NAME" property="formName" jdbcType="VARCHAR" />
        <result column="is_permission" property="isPermission" jdbcType="TINYINT" />
        <result column="permission_type" property="permissionType" jdbcType="VARCHAR" />
        <result column="permission_type_values" property="permissionTypeValues" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="ModelExtendedBaseResultMap" type="com.ctsi.activiti.bpmn.entity.CscpProcExtended">
        <id column="ID_"  property="id"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEFINITION_KEY_"  property="processDefinitionKey"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEFINITION_ID_"  property="processDefinitionId"   jdbcType="VARCHAR" />
        <result column="PROCESS_DEPLOYMENT_ID_"  property="processDeploymentId"   jdbcType="VARCHAR" />
        <result column="NAME_"  property="name"   jdbcType="VARCHAR" />
        <result column="MAX_VERSION_"  property="maxVersion"  jdbcType="VARCHAR" />
        <result column="DEL"  property="del"  jdbcType="VARCHAR" />
        <result column="CREATE_TIME"  property="createTime"  jdbcType="VARCHAR" />
        <result column="UPDATE_TIME"  property="updateTime"  jdbcType="VARCHAR" />
        <result column="TYPE_ID"  property="typeId"   jdbcType="VARCHAR" />
        <result column="FORM_TYPE"  property="formType"   jdbcType="VARCHAR" />
        <result column="OPERATE_TYPE"  property="operateType"   jdbcType="VARCHAR"/>
        <result column="SEQ_ID" property="seqId" jdbcType="VARCHAR" />
        <result column="FORM_ID" property="formId" jdbcType="VARCHAR" />
        <result column="FORM_NAME" property="formName" jdbcType="VARCHAR" />
        <result column="ONLINE_FLAG" property="onlineFlag" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="res" type="com.ctsi.activiti.bpmn.entity.TestEntity">
        <id column="ID_"  property="id"   jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="TASK_DEF_KEY_" property="taskDefKey" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="NODE_NAME" property="nodeName" jdbcType="VARCHAR" />
        <result column="TASK_TITLE" property="taskTitle" jdbcType="VARCHAR" />
        <result column="DEAL_TIME" property="dealTime" jdbcType="VARCHAR" />
        <result column="PROC_NAME" property="procName" jdbcType="VARCHAR" />
        <result column="CREATE_TIME_" property="createTime" jdbcType="VARCHAR" />
    </resultMap>




    <select id="queryPageModelExtendedList" parameterType="com.ctsi.activiti.core.vo.PageQuery" resultMap="ModelExtendedBaseResultMap">
        select c1.ID_, c1.PROCESS_DEFINITION_KEY_, c1.PROCESS_DEFINITION_ID_, c1.PROCESS_DEPLOYMENT_ID_,c1.NAME_, c1.MAX_VERSION_, c1.DEL, c1.XML_, c1.SVG_, c1.TYPE_ID, c1.FORM_TYPE, c1.OPERATE_TYPE,c1.CREATE_TIME,c1.SEQ_ID,c1.FORM_ID,c1.FORM_NAME,c1.ONLINE_FLAG
        from CSCP_PROC c1 where
        PROCESS_DEFINITION_ID_=(select max(PROCESS_DEFINITION_ID_) from cscp_proc c2 WHERE c1.PROCESS_DEFINITION_KEY_=c2.PROCESS_DEFINITION_KEY_ AND c2.MAX_VERSION_ = 1 AND c2.ONLINE_FLAG = '1')
        AND c1.MAX_VERSION_ = 1 AND c1.ONLINE_FLAG = '1'
        <if test="obj!=null">
            <if test="obj.name != null and obj.name !=''">
                and c1.NAME_ like concat('%',#{obj.name},'%')
            </if>
            <if test="obj.typeId != null and obj.typeId !=''">
                and c1.TYPE_ID = #{obj.typeId,jdbcType=VARCHAR}
            </if>
            <if test="obj.listTypeId != null and obj.listTypeId.size > 0">
                and c1.TYPE_ID IN
                <foreach collection="obj.listTypeId" item="tid" index="index" open="(" close=")" separator=",">
                    '${tid}'
                </foreach>
            </if>
        </if>
        order by c1.CREATE_TIME DESC
    </select>


    <select id="queryPageCancellationProcessList" parameterType="com.ctsi.business.domain.Cscp" resultType="com.ctsi.common.utils.PageData">
        SELECT DISTINCT RES.PROC_INST_ID_, RES.TASK_DEF_KEY_, RES.ID_, RES.PROC_DEF_ID_,
        RES.NAME_ AS 'NODE_NAME',
        CPB.TITLE AS TASK_TITLE, CPB.PROC_END_TIME AS 'DEAL_TIME', CP.NAME_ AS 'PROC_NAME',RES.CREATE_TIME_
        FROM ACT_RU_TASK RES LEFT JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES.ID_
        LEFT JOIN cscp_proc_base CPB ON CPB.PROC_INST_ID = RES.PROC_INST_ID_
        LEFT JOIN cscp_proc CP ON CP.PROCESS_DEFINITION_ID_ = RES.PROC_DEF_ID_
        WHERE (RES.ASSIGNEE_ = #{obj.assignee,jdbcType=VARCHAR} OR (RES.ASSIGNEE_ IS NULL AND I.TYPE_ = 'candidate' AND (I.USER_ID_ = #{obj.assignee,jdbcType=VARCHAR} )))
        AND CPB.bpm_status = '2'
        AND CP.OPERATE_TYPE != '3'
        <if test="obj!=null">
            <if test="obj.title != null and obj.title !=''">
                and CPB.title like concat('%',#{obj.title},'%')
            </if>
           <if test="obj.processName != null and obj.processName !=''">
               and CP.NAME_ like concat('%',#{obj.processName},'%')
            </if>
        </if>
        order by RES.CREATE_TIME_ DESC
    </select>

    <select id="getExpressionByNode" parameterType="com.ctsi.common.utils.PageData" resultType="com.ctsi.common.utils.PageData">
            select
                    t.*
              from
                    cscp_proc_expression t
             where
                    t.PROCDEFID = #{PROCDEFID}
               and
                    t.CURRENTNODEID = #{CURRENTNODEID}
          order by
                    t.SHOWNUM
    </select>

    <select id="queryPageViewTaskList" parameterType="com.ctsi.activiti.core.vo.PageQuery" resultType="com.ctsi.common.utils.PageData">
        SELECT
                cpv.ID, cpv.SUBJECT, cpv.SEQ_NO, cpv.PROC_STATUS, cpv.PROC_NAME,
                cpv.COPY_OPINION, cpv.ACT_DEF_ID, cpv.FORM_DATA_ID, cpv.FORM_DEF_ID,
                cpv.ACT_NAME, cpv.PROC_INST_ID, cpv.COPY_TIME, cpv.CRAFTER_NAME, cpv.PROC_DEF_ID,
                cpb.MODELKEY
          FROM
                cscp_proc_viewtask cpv
     LEFT JOIN
                cscp_proc_base cpb ON cpb.PROC_INST_ID = cpv.PROC_INST_ID
         WHERE
                cpv.COPY_USER_ID = #{obj.assignee}
           AND
                cpv.COPY_STATUS = #{obj.flag}
            <if test="obj.title !=null and obj.title !=''">
                and cpv.SUBJECT like concat('%',concat(#{obj.title},'%'))
            </if>
            <if test="obj.processName !=null and obj.processName !=''">
                and cpv.PROC_NAME like concat('%',concat(#{obj.processName},'%'))
            </if>
      ORDER BY
                cpv.COPY_TIME DESC
    </select>

    <insert id="viewTask" parameterType="com.ctsi.common.utils.PageData">
            insert into cscp_proc_viewtask
                        (
                            ID,
                            PROC_DEF_ID,
                            PROC_INST_ID,
                            ACT_DEF_ID,
                            FORM_DEF_ID,
                            FORM_DATA_ID,
                            SEQ_NO,
                            PROC_NAME,
                            ACT_NAME,
                            PROC_STATUS,
                            SUBJECT,
                            CRAFTER_ID,
                            CRAFTER_NAME,
                            CREATE_DEPT,
                            COPY_SEND_DEPT,
                            COPY_USER_ID,
                            COPY_USER_NAME,
                            COPY_OPINION,
                            COPY_TIME,
                            COPY_STATUS,
                            PROC_ORGAN
                        )
                values
                        (
                            #{ID},
                            #{PROC_DEF_ID},
                            #{PROC_INST_ID},
                            #{ACT_DEF_ID},
                            #{FORM_DEF_ID},
                            #{FORM_DATA_ID},
                            #{SEQ_NO},
                            #{PROC_NAME},
                            #{ACT_NAME},
                            #{PROC_STATUS},
                            #{SUBJECT},
                            #{CRAFTER_ID},
                            #{CRAFTER_NAME},
                            #{CREATE_DEPT},
                            #{COPY_SEND_DEPT},
                            #{COPY_USER_ID},
                            #{COPY_USER_NAME},
                            #{COPY_OPINION},
                            #{COPY_TIME},
                            #{COPY_STATUS},
                            #{PROC_ORGAN}
                        )

    </insert>

    <update id="setViewTaskRead" parameterType="com.ctsi.common.utils.PageData">
        update cscp_proc_viewtask
        set COPY_STATUS = '0'
        where ID = #{VIEW_ID}
    </update>

    <select id="getViewTask" parameterType="com.ctsi.common.utils.PageData" resultType="com.ctsi.common.utils.PageData">
        select t.* from cscp_proc_viewtask t where t.ID = #{VIEW_ID}
    </select>

    <select id="queryPageAllTaskList" parameterType="com.ctsi.business.domain.Cscp" resultType="com.ctsi.common.utils.PageData">
        SELECT
        DISTINCT (ARE.ID_) AS PROCESSINSTID,
        ARE.PROC_DEF_ID_ AS PROCESSDEFID,
        DATE_FORMAT(AHP.START_TIME_, '%Y-%m-%d %H:%i:%s') AS SENDTIME,
        WBW.title AS title, WBW.FORM_DEF_ID, WBW.FORM_DATA_ID AS "FORM_ID",
        WBW.create_by AS "create_by" , WBW.create_name AS "create_name", WBW.PROC_SEQ_NO AS "SEQ_NO",
        D.NAME_ AS "PROCESS_NAME", D.KEY_ AS "PROCESS_KEY",
        WBW.bpm_status as bpmStatus
        FROM
        ACT_RU_EXECUTION ARE
        LEFT JOIN
        cscp_proc_base WBW
        ON
        ARE.ID_ = WBW.PROC_INST_ID

        LEFT JOIN
        ACT_HI_PROCINST AHP
        ON
        ARE.ID_ = AHP.PROC_INST_ID_
        LEFT JOIN
        ACT_RE_PROCDEF D
        ON
        ARE.PROC_DEF_ID_ = D.ID_
        WHERE

        1=1
        AND
        WBW.PROC_INST_ID IS NOT NULL
        <if test="obj!=null">
            <if test="obj.processName != null and obj.processName != ''">
                <![CDATA[ and D.NAME_ like concat('%',concat(#{obj.processName},'%'))]]>
            </if>
            <if test="obj.title != null and obj.title != ''">
                <![CDATA[ and wbw.title like concat('%',concat(#{obj.title},'%'))]]>
            </if>
        </if>
        ORDER BY
        SENDTIME DESC

    </select>

    <select id="queryPageOverTimeTask" parameterType="com.ctsi.activiti.core.vo.PageQuery" resultType="com.ctsi.common.utils.PageData">
        SELECT ext.PROCINST_ID,ext.PROCDEF_ID,ext.ACTIVITY_ID,
        ext.TASK_ID,ext.USER_ID,
        CASE WHEN ext.deal_user_id IS NULL THEN ext.user_id ELSE ext.deal_user_id END AS DEALUSERID,
        ext.create_TIME,ext.LIMIT_TIME,ext.END_TIME,tp.TYPE_NAME,
        bs.FORM_MAIN_NAME AS PROCNAME,nd.NAME AS NODENAME
        FROM cscp_proc_task_extend ext
        LEFT JOIN cscp_proc_base bs ON ext.procinst_id = bs.proc_inst_id
        LEFT JOIN cscp_proc_node nd ON ext.procdef_id = nd.process_definition_id AND ext.activity_id = nd.node_key
        LEFT JOIN cscp_proc prc ON ext.procdef_id = prc.process_definition_id_
        LEFT JOIN cscp_proc_type tp ON prc.type_id = tp.type_id
        WHERE limittime IS NOT NULL
        AND
        (
          (CONCAT('',SYSDATE()) > ext.limit_time AND ext.end_time IS NULL)
            OR
          (end_time IS NOT NULL AND ext.end_time > ext.limittime)
        )
        <if test="obj!=null">
            <if test='obj.assignee != null and obj.assignee != "" '>
                AND ext.USER_ID = #{obj.assignee}
            </if>
            <if test='obj.proc_key_ != null and obj.proc_key_ != "" '>
                AND prc.PROCESS_DEFINITION_KEY_ = #{obj.proc_key_}
            </if>
            <if test='obj.processName != null and obj.processName != "" '>
                AND prc.NAME_ LIKE CONCAT('%',CONCAT(#{obj.processName},'%'))
            </if>
            <if test='obj.title != null and obj.title != "" '>
                AND bs.FORM_MAIN_NAME LIKE CONCAT('%',CONCAT(#{obj.title},'%'))
            </if>
        </if>
    </select>

    <select id="queryProcessGraphCount" parameterType="map" resultType="integer">
        SELECT COUNT(*) FROM cscp_proc_base b  LEFT JOIN cscp_proc_type t ON b.PROC_TYPE_ID=t.TYPE_ID WHERE substring(b.CREATE_TIME,1,7)=#{month}
        <if test="appCode !=null and appCode !=''">
            and b.PROC_TYPE_ID is NOT NULL and t.app_code=#{appCode}
        </if>
    </select>

    <select id="queryProcessPieCount" parameterType="map" resultType="integer">
        SELECT COUNT(*) FROM cscp_proc_base b  LEFT JOIN cscp_proc_type t ON b.PROC_TYPE_ID=t.TYPE_ID WHERE substring(CREATE_TIME,1,4)=#{years}
        <if test="appCode !=null and appCode !=''">
            and t.app_code=#{appCode}
        </if>
        <if test="typeId !=null and typeId !=''">
            and b.PROC_TYPE_ID =#{typeId}
        </if>
    </select>


    <update id="updateActHiActinst" parameterType="string">
        update act_hi_actinst set END_TIME_=NOW() where TASK_ID_=#{id} and PROC_INST_ID_=#{processInstanceId}
    </update>

    <select id="queryPageTaskListMultiAssign" parameterType="com.ctsi.activiti.core.model.TaskQueryParam" resultType="org.activiti.engine.impl.persistence.entity.TaskEntityImpl">
      SELECT * FROM (
        SELECT DISTINCT
        RES1.PROC_INST_ID_ AS processInstanceId,
        RES1.ID_ AS id,
        RES1.NAME_ AS NAME,
        RES1.TASK_DEF_KEY_ AS taskDefinitionKey,
        RES1.CREATE_TIME_ AS createTime,
        RES1.PROC_DEF_ID_ AS processDefinitionId,
        RES1.ASSIGNEE_ AS assignee
        FROM ACT_RU_TASK RES1 INNER JOIN ACT_RE_PROCDEF D ON RES1.PROC_DEF_ID_=D.ID_
        INNER JOIN ACT_RU_VARIABLE V ON V.PROC_INST_ID_=RES1.PROC_INST_ID_
        LEFT JOIN cscp_proc_base B ON b.PROC_INST_ID=RES1.PROC_INST_ID_
        WHERE RES1.SUSPENSION_STATE_=1 <!--作废状态  1正常  2作废-->
        <if test="obj.taskCandidateOrAssignedIn != null">
          AND RES1.ASSIGNEE_ in
          <foreach collection="obj.taskCandidateOrAssignedIn" item="assign_" index="idx" open="(" separator="," close=")">
              #{obj.taskCandidateOrAssignedIn[${idx}]}
          </foreach>
        </if>
        <if test="obj.processDefinitionKey != null and obj.processDefinitionKey !='' ">
          AND RES1.TASK_DEF_KEY_ = #{obj.processDefinitionKey}
        </if>
        <if test="obj.processDefinitionKeyIn != null">
            AND D.KEY_ in
            <foreach collection="obj.processDefinitionKeyIn" item="defKey_" index="idx" open="(" separator="," close=")">
                #{obj.processDefinitionKeyIn[${idx}]}
            </foreach>
        </if>
        <if test="obj.processDefinitionNameLike != null and obj.processDefinitionNameLike != '' ">
          AND D.NAME_ LIKE #{obj.processDefinitionNameLike}
        </if>
        <if test="obj.title!=null and obj.title !=''">
            AND B.title LIKE #{obj.title}
        </if>
        <if test="obj.ext1!=null and obj.ext1 !=''">
            AND B.ext1 = #{obj.ext1}
        </if>
        <if test="obj.ext5!=null and obj.ext5 !=''">
            AND B.ext5 = #{obj.ext5}
        </if>
        <if test="obj.ext2!=null and obj.ext2 !=''">
            AND B.ext2 = #{obj.ext2}
        </if>
        <if test="obj.ext3!=null and obj.ext3 !=''">
            AND B.ext3 = #{obj.ext3}
        </if>
        <if test="obj.ext4!=null and obj.ext4 !=''">
            AND B.ext4 = #{obj.ext4}
        </if>

    UNION ALL
        SELECT DISTINCT
        RES1.PROC_INST_ID_ AS processInstanceId,
        RES1.ID_ AS id,
        RES1.NAME_ AS NAME,
        RES1.TASK_DEF_KEY_ AS taskDefinitionKey,
        RES1.CREATE_TIME_ AS createTime,
        RES1.PROC_DEF_ID_ AS processDefinitionId,
        I.USER_ID_ AS assignee
            FROM ACT_RU_TASK RES1  INNER JOIN ACT_RE_PROCDEF D ON RES1.PROC_DEF_ID_=D.ID_
            INNER JOIN ACT_RU_VARIABLE V ON V.PROC_INST_ID_=RES1.PROC_INST_ID_
            LEFT JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES1.ID_
            LEFT JOIN cscp_proc_base B ON b.PROC_INST_ID=RES1.PROC_INST_ID_
        WHERE RES1.ASSIGNEE_ IS NULL and RES1.SUSPENSION_STATE_=1 <!--作废状态  1正常  2作废-->
        <if test="obj.taskCandidateOrAssignedIn != null">
            AND I.TYPE_ = 'candidate'
            AND I.USER_ID_ in
            <foreach collection="obj.taskCandidateOrAssignedIn" item="assign_" index="idx" open="(" separator="," close=")">
                #{obj.taskCandidateOrAssignedIn[${idx}]}
            </foreach>
        </if>
        <if test="obj.processDefinitionKey != null and obj.processDefinitionKey !='' ">
            AND RES1.TASK_DEF_KEY_ = #{obj.processDefinitionKey}
        </if>
        <if test="obj.processDefinitionKeyIn != null">
            AND D.KEY_ in
            <foreach collection="obj.processDefinitionKeyIn" item="defKey_" index="idx" open="(" separator="," close=")">
                #{obj.processDefinitionKeyIn[${idx}]}
            </foreach>
        </if>
        <if test="obj.processDefinitionNameLike != null and obj.processDefinitionNameLike != '' ">
            AND D.NAME_ LIKE #{obj.processDefinitionNameLike}
        </if>
        <if test="obj.title!=null and obj.title !=''">
            AND B.title LIKE #{obj.title}
        </if>
        <if test="obj.ext1!=null and obj.ext1 !=''">
            AND B.ext1 = #{obj.ext1}
        </if>
        <if test="obj.ext5!=null and obj.ext5 !=''">
            AND B.ext5 = #{obj.ext5}
        </if>
        <if test="obj.ext2!=null and obj.ext2 !=''">
            AND B.ext2 = #{obj.ext2}
        </if>
        <if test="obj.ext3!=null and obj.ext3 !=''">
            AND B.ext3 = #{obj.ext3}
        </if>
        <if test="obj.ext4!=null and obj.ext4 !=''">
            AND B.ext4 = #{obj.ext4}
        </if>
        ) AS tasktmp
    ORDER BY createTime DESC
    </select>
    <select id="queryPageFinishedTaskListForPage" parameterType="com.ctsi.activiti.core.vo.PageQuery" resultType="org.activiti.engine.impl.persistence.entity.HistoricTaskInstanceEntityImpl">
        SELECT DISTINCT
        RES.PROC_DEF_ID_ AS processDefinitionId,
        RES.PROC_INST_ID_ AS processInstanceId,
        RES.TASK_DEF_KEY_ AS taskDefinitionKey,
        RES.EXECUTION_ID_ AS executionId,
        RES.NAME_ AS name,
        RES.ASSIGNEE_ AS assignee,
        RES.START_TIME_ AS startTime,
        RES.END_TIME_ AS endTime,
        RES. DELETE_REASON_ AS deleteReason
        FROM ACT_HI_TASKINST RES
        LEFT JOIN CSCP_PROC_BASE BASE ON  RES.PROC_INST_ID_ =BASE.PROC_INST_ID
        LEFT JOIN CSCP_PROC PROC ON PROC.PROCESS_DEFINITION_ID_=RES.PROC_DEF_ID_
        WHERE RES.END_TIME_ IS NOT NULL
        AND RES.DELETE_REASON_ IS NULL
        <if test="obj.processDefinitionKeyIn != null">
            AND PROC.PROCESS_DEFINITION_KEY_ in
            <foreach collection="obj.processDefinitionKeyIn" item="defKey_" index="idx" open="(" separator="," close=")">
                #{obj.processDefinitionKeyIn[${idx}]}
            </foreach>
        </if>
        <if test="obj.title!=null and obj.title !=''">
            AND BASE.title LIKE #{obj.title}
        </if>
        <if test="obj.ext1!=null and obj.ext1 !=''">
            AND BASE.ext1 = #{obj.ext1}
        </if>
        <if test="obj.ext5!=null and obj.ext5 !=''">
            AND BASE.ext5 = #{obj.ext5}
        </if>
        <if test="obj.ext2!=null and obj.ext2 !=''">
            AND BASE.ext2 = #{obj.ext2}
        </if>
        <if test="obj.ext3!=null and obj.ext3 !=''">
            AND BASE.ext3 = #{obj.ext3}
        </if>
        <if test="obj.ext4!=null and obj.ext4 !=''">
            AND BASE.ext4 = #{obj.ext4}
        </if>
        <if test="obj.taskCandidateOrAssignedInStr!=null and obj.taskCandidateOrAssignedInStr !=''">
            and RES.ASSIGNEE_ =  #{obj.taskCandidateOrAssignedInStr}
        </if>
        ORDER BY RES.START_TIME_ DESC
    </select>

    <select id="queryPageModelListNoAdd" resultType="com.ctsi.activiti.bpmn.entity.CscpProc">
        select c1.ID_, c1.PROCESS_DEFINITION_KEY_, c1.PROCESS_DEFINITION_ID_, c1.PROCESS_DEPLOYMENT_ID_,c1.NAME_, c1.MAX_VERSION_, c1.TYPE_ID, c1.FORM_TYPE,
        c1.OPERATE_TYPE,c1.CREATE_TIME,c1.SEQ_ID,c1.FORM_ID,c1.FORM_NAME,c1.is_permission,c1.permission_type,c1.permission_type_values,c1.sort
        from CSCP_PROC c1 where   c1.ID_=(select max(ID_) from cscp_proc c2 WHERE c1.PROCESS_DEFINITION_KEY_=c2.PROCESS_DEFINITION_KEY_ and c2.deleted = 0
                                              and c2.PROCESS_DEFINITION_ID_ != '0' and c2.PROCESS_DEPLOYMENT_ID_ !='0' limit 1)
        and  c1.deleted = 0 and  c1.PROCESS_DEFINITION_ID_ != '0' and c1.PROCESS_DEPLOYMENT_ID_ !='0'
        <if test="obj!=null">
            <if test="obj.name != null and obj.name !=''">
                and c1.NAME_ like concat('%',#{obj.name},'%')
            </if>
            <if test="obj.typeId != null and obj.typeId !=''">
                and c1.TYPE_ID = #{obj.typeId,jdbcType=VARCHAR}
            </if>
            <if test="obj.companyId != null and obj.companyId !=''">
                and c1.company_id = #{obj.companyId}
            </if>
            <if test="obj.processDefinitionKey != null and obj.processDefinitionKey !=''">
                and c1.PROCESS_DEFINITION_KEY_ = #{obj.processDefinitionKey,jdbcType=VARCHAR}
            </if>
            <if test="obj.onlineFlag != null and obj.onlineFlag !=''">
                and c1.ONLINE_FLAG = #{obj.onlineFlag,jdbcType=VARCHAR}
            </if>
            <if test="obj.createBy != null and obj.createBy !=''">
                and c1.create_by = #{obj.createBy,jdbcType=VARCHAR}
            </if>
            <if test="obj.modelKeyList !=null and obj.modelKeyList.size>0">
                and c1.PROCESS_DEFINITION_KEY_ in
                <foreach collection="obj.modelKeyList" index="index" item="tid" open="(" separator="," close=")">
                    '${tid}'
                </foreach>
            </if>
            <if test="obj.modelFormIdList !=null and obj.modelFormIdList.size>0">
                and c1.form_id in
                <foreach collection="obj.modelFormIdList" index="index" item="tid" open="(" separator="," close=")">
                    '${tid}'
                </foreach>
            </if>

        </if>
        order by c1.sort

    </select>


    <select id="queryPageModelList" parameterType="com.ctsi.activiti.bpmn.entity.CscpProc" resultMap="ModelBaseResultMap">
        select c1.ID_, c1.PROCESS_DEFINITION_KEY_, c1.PROCESS_DEFINITION_ID_, c1.PROCESS_DEPLOYMENT_ID_,c1.NAME_, c1.MAX_VERSION_, c1.TYPE_ID, c1.FORM_TYPE,
        c1.OPERATE_TYPE,c1.CREATE_TIME,c1.SEQ_ID,c1.FORM_ID,c1.FORM_NAME,c1.is_permission,c1.permission_type,c1.permission_type_values,c1.sort
        from CSCP_PROC c1 where c1.id_=(select max(c2.id_) from cscp_proc c2 WHERE c1.PROCESS_DEFINITION_KEY_=c2.PROCESS_DEFINITION_KEY_ and deleted = 0  limit 1)
        and c1.deleted = 0
        <if test="obj!=null">
            <if test="obj.name != null and obj.name !=''">
                and c1.NAME_ like concat('%',#{obj.name},'%')
            </if>
            <if test="obj.typeId != null and obj.typeId !=''">
                and c1.TYPE_ID = #{obj.typeId,jdbcType=VARCHAR}
            </if>
            <if test="obj.companyId != null and obj.companyId !=''">
                and c1.company_id = #{obj.companyId}
            </if>
            <if test="obj.processDefinitionKey != null and obj.processDefinitionKey !=''">
                and c1.PROCESS_DEFINITION_KEY_ = #{obj.processDefinitionKey,jdbcType=VARCHAR}
            </if>
            <if test="obj.onlineFlag != null and obj.onlineFlag !=''">
                and c1.ONLINE_FLAG = #{obj.onlineFlag,jdbcType=VARCHAR}
            </if>
            <if test="obj.createBy != null and obj.createBy !=''">
                and c1.create_by = #{obj.createBy,jdbcType=VARCHAR}
            </if>
            <if test="obj.modelKeyList !=null and obj.modelKeyList.size>0">
                and c1.PROCESS_DEFINITION_KEY_ in
                <foreach collection="obj.modelKeyList" index="index" item="tid" open="(" separator="," close=")">
                    '${tid}'
                </foreach>
            </if>
            <if test="obj.modelFormIdList !=null and obj.modelFormIdList.size>0">
                and c1.form_id in
                <foreach collection="obj.modelFormIdList" index="index" item="tid" open="(" separator="," close=")">
                    '${tid}'
                </foreach>
            </if>

        </if>
        order by c1.sort
    </select>

    <select id="queryPageModelListBytenantIdList" resultType="com.ctsi.activiti.bpmn.dto.CscpProcDTO" resultMap="ModelBaseResultTenantIdMap">
        select c1.ID_, c1.PROCESS_DEFINITION_KEY_, c1.PROCESS_DEFINITION_ID_, c1.PROCESS_DEPLOYMENT_ID_,c1.NAME_, c1.MAX_VERSION_, c1.TYPE_ID, c1.FORM_TYPE,
        c1.OPERATE_TYPE,c1.CREATE_TIME,c1.SEQ_ID,c1.FORM_ID,c1.FORM_NAME,c1.is_permission,c1.permission_type,c1.permission_type_values,c1.tenant_id
        from CSCP_PROC c1 where c1.CREATE_TIME=(select max(c2.CREATE_TIME) from cscp_proc c2 WHERE c1.PROCESS_DEFINITION_KEY_=c2.PROCESS_DEFINITION_KEY_ limit 1)
        and c1.deleted = 0
        <if test="obj!=null">
            <if test="obj.name != null and obj.name !=''">
                and c1.NAME_ like concat('%',#{obj.name},'%')
            </if>
            <if test="obj.typeId != null and obj.typeId !=''">
                and c1.TYPE_ID = #{obj.typeId,jdbcType=VARCHAR}
            </if>
            <if test="obj.companyId != null and obj.companyId !=''">
                and c1.company_id = #{obj.companyId}
            </if>
            <if test="obj.processDefinitionKey != null and obj.processDefinitionKey !=''">
                and c1.PROCESS_DEFINITION_KEY_ = #{obj.processDefinitionKey,jdbcType=VARCHAR}
            </if>
            <if test="obj.onlineFlag != null and obj.onlineFlag !=''">
                and c1.ONLINE_FLAG = #{obj.onlineFlag,jdbcType=VARCHAR}
            </if>
            <if test="obj.createBy != null and obj.createBy !=''">
                and c1.create_by = #{obj.createBy,jdbcType=VARCHAR}
            </if>
            <if test="obj.modelKeyList !=null and obj.modelKeyList.size>0">
                and c1.PROCESS_DEFINITION_KEY_ in
                <foreach collection="obj.modelKeyList" index="index" item="tid" open="(" separator="," close=")">
                    '${tid}'
                </foreach>
            </if>
            <if test = "obj.tenantIdList != null  and obj.tenantIdList.size() > 0">
                AND c1.tenant_id IN
                <foreach collection="obj.tenantIdList" item="tenant_id" open="(" close=")" separator=",">
                    #{tenant_id}
                </foreach>
            </if>
            <if test="obj.modelFormIdList !=null and obj.modelFormIdList.size>0">
                and c1.form_id in
                <foreach collection="obj.modelFormIdList" index="index" item="tid" open="(" separator="," close=")">
                    '${tid}'
                </foreach>
            </if>

        </if>
        order by c1.sort

    </select>
</mapper>