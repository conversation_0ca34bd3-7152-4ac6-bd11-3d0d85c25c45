<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpTaskExtRepository">
  <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpTaskExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 18:09:18 CST 2019.
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROC_INST_ID" jdbcType="VARCHAR" property="procInstId" />
    <result column="PROC_DEF_ID" jdbcType="VARCHAR" property="procDefId" />
    <result column="PROC_ACT_ID" jdbcType="VARCHAR" property="procActId" />
    <result column="TASKID" jdbcType="VARCHAR" property="taskid" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="USERDEPTID" jdbcType="VARCHAR" property="userdeptid" />
    <result column="DEALUSERID" jdbcType="VARCHAR" property="dealuserid" />
    <result column="DEALDEPTID" jdbcType="VARCHAR" property="dealdeptid" />
    <result column="STARTTIME" jdbcType="VARCHAR" property="starttime" />
    <result column="ENDTIME" jdbcType="VARCHAR" property="endtime" />
    <result column="ISEND" jdbcType="VARCHAR" property="isend" />
    <result column="CREATTIME" jdbcType="VARCHAR" property="creattime" />
    <result column="CBSTAT" jdbcType="VARCHAR" property="cbstat" />
  </resultMap>



</mapper>