<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.business.repository.CscpInformationRepository">
    <resultMap id="BaseResultMap" type="com.ctsi.business.domain.CscpInformation">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Apr 15 14:19:49 CST 2020.
        -->
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="SUBJECT" jdbcType="VARCHAR" property="subject" />
        <result column="BID" jdbcType="VARCHAR" property="bid" />
        <result column="SYS_MODULE" jdbcType="VARCHAR" property="sysModule" />
        <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
        <result column="DESPATCHER" jdbcType="VARCHAR" property="despatcher" />
        <result column="DESPATCHER_NAME" jdbcType="VARCHAR" property="despatcherName" />
        <result column="DESP_DEPT" jdbcType="VARCHAR" property="despDept" />
        <result column="DESP_NAME" jdbcType="VARCHAR" property="despName" />
        <result column="ACCEPTER" jdbcType="VARCHAR" property="accepter" />
        <result column="ACCEPT_NAME" jdbcType="VARCHAR" property="acceptName" />
        <result column="ACCEPT_DEPT" jdbcType="VARCHAR" property="acceptDept" />
        <result column="ACCEPT_DEPTNAME" jdbcType="VARCHAR" property="acceptDeptname" />
        <result column="READ_STATUS" jdbcType="VARCHAR" property="readStatus" />
    </resultMap>


</mapper>