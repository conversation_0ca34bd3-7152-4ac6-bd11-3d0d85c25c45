CREATE TABLE "myapp"."cscp_app"
(
    "ID" VARCHAR(64) NOT NULL,
    "APP_CODE" VARCHAR(64) NOT NULL,
    "APP_NAME" VARCHAR(64) NOT NULL,
    "SECRET_KEY" VARCHAR(100) NOT NULL,
    "REMARKS" VARCHAR(500),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_app" IS '第三方应用的工作流分类的顶级';COMMENT ON COLUMN "myapp"."cscp_app"."ID" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_app"."APP_CODE" IS '编码';
COMMENT ON COLUMN "myapp"."cscp_app"."APP_NAME" IS '名称';
COMMENT ON COLUMN "myapp"."cscp_app"."SECRET_KEY" IS '密钥';
COMMENT ON COLUMN "myapp"."cscp_app"."REMARKS" IS '备注';




CREATE TABLE "myapp"."cscp_audit_content"
(
    "ID" VARCHAR(64) NOT NULL,
    "DELEGATOR_NAME" VARCHAR(500),
    "DELEGATOR_ID" VARCHAR(200),
    "AUDITOR_NAME" VARCHAR(500),
    "PROC_DEF_ID" VARCHAR(64),
    "PROC_INST_ID" VARCHAR(64),
    "ACT_NAME" VARCHAR(200),
    "ACT_ID" VARCHAR(64),
    "AUDIT_CONTENT" VARCHAR(1000),
    "AUDITOR_ID" VARCHAR(200),
    "CLIENT_TYPE" VARCHAR(20),
    "AUDIT_STATUS" VARCHAR(1),
    "AUDIT_TIME" VARCHAR(20),
    "COMMENTS_FORM_ID" VARCHAR(500),
    "business_id" VARCHAR(20),
    "root_proc_inst_id" VAR, CHAR(20),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_audit_content" IS '审核意见表';COMMENT ON COLUMN "myapp"."cscp_audit_content"."ID" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."DELEGATOR_NAME" IS '委托人姓名';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."DELEGATOR_ID" IS '委托人id';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."AUDITOR_NAME" IS '审核人';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."PROC_DEF_ID" IS '流程定义id';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."PROC_INST_ID" IS '流程实例id';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."ACT_NAME" IS '审核名称';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."ACT_ID" IS '流程步骤';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."AUDIT_CONTENT" IS '审核内容';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."AUDITOR_ID" IS '审核员id';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."CLIENT_TYPE" IS '审核客户端';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."AUDIT_STATUS" IS '审核状态0正常，1回退 2是移交，3跳转';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."AUDIT_TIME" IS '审核时间';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."COMMENTS_FORM_ID" IS '意见栏的表单id';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."business_id" IS '业务表的id';
COMMENT ON COLUMN "myapp"."cscp_audit_content"."root_proc_inst_id" IS '父节点的流程实例id';


CREATE  INDEX "INDEX503425397552500" ON "myapp"."cscp_audit_content"("PROC_INST_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "index_businessId" ON "myapp"."cscp_audit_content"("business_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."cscp_audit_log"
(
    "PROCESS_STATUS" VARCHAR(1),
    "DELEGATOR_ID" VARCHAR(200),
    "AUDITOR_NAME" VARCHAR(500),
    "PROC_INST_ID" VARCHAR(64),
    "ACT_ID" VARCHAR(64),
    "PROC_DEF_ID" VARCHAR(64),
    "OPERATE_TYPE" VARCHAR(50),
    "AUDIT_CONTENT" VARCHAR(1000),
    "AUDITOR_ID" VARCHAR(200),
    "EXT1" VARCHAR(100),
    "EXT2" VARCHAR(100),
    "DELEGATOR_NAME" VARCHAR(500),
    "CLIENT_TYPE" VARCHAR(1),
    "EXT3" VARCHAR(100),
    "EXT4" VARCHAR(100),
    "EXT5" VARCHAR(100),
    "AUDIT_STATUS" VARCHAR(1),
    "T, ITLE" VARCHAR(500),
    "ID" VARCHAR(64) NOT NULL,
    "ACT_NAME" VARCHAR(200),
    "AUDIT_TIME" VARCHAR(20),
    "to_node_name" VARCHAR(50)) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_audit_log" IS '审核日志表';COMMENT ON COLUMN "myapp"."cscp_audit_log"."PROCESS_STATUS" IS '进程状态';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."DELEGATOR_ID" IS '委托人id';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."AUDITOR_NAME" IS '审核人名称';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."PROC_INST_ID" IS '过程id';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."ACT_ID" IS '行为id';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."PROC_DEF_ID" IS '过程定义id';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."OPERATE_TYPE" IS '操作类型';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."AUDIT_CONTENT" IS '审核内容';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."AUDITOR_ID" IS '审核人id';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."DELEGATOR_NAME" IS '授权人姓名';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."AUDIT_STATUS" IS '审核状态';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."TITLE" IS '标题';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."ID" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."ACT_NAME" IS '行为人姓名';
COMMENT ON COLUMN "myapp"."cscp_audit_log"."AUDIT_TIME" IS '审核时间';




CREATE TABLE "myapp"."cscp_btn"
(
    "DISPLAY_ORDER" INT,
    "FUNCTION_NAME" VARCHAR(200),
    "DESCRIPTION" VARCHAR(500),
    "SUB_TYPE" VARCHAR(200),
    "IS_DEFAULT" VARCHAR(1),
    "ACTION_NAME" VARCHAR(200) NOT NULL,
    "ACTION_ID" VARCHAR(200),
    "CLASS_NAME" VARCHAR(200),
    "ACTION_TYPE" VARCHAR(200),
    "id" BIGINT NOT NULL,
    "create_by" BIGINT,
    "create_name" VARCHAR(100),
    "create_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "update_time" TIMESTAMP(0),
    "department_id" BIGINT,
    "co, mpany_id" BIGINT,
    "tenant_id" BIGINT,
    "deleted" INT,
    NOT CLUSTER PRIMARY KEY("id"),
    CONSTRAINT "INDEX_FUNCTION_NAME" UNIQUE("FUNCTION_NAME")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_btn" IS '表单中按钮的表';COMMENT ON COLUMN "myapp"."cscp_btn"."DISPLAY_ORDER" IS '按钮显示顺序';
COMMENT ON COLUMN "myapp"."cscp_btn"."FUNCTION_NAME" IS '函数名称';
COMMENT ON COLUMN "myapp"."cscp_btn"."DESCRIPTION" IS '描述';
COMMENT ON COLUMN "myapp"."cscp_btn"."SUB_TYPE" IS '子类型';
COMMENT ON COLUMN "myapp"."cscp_btn"."IS_DEFAULT" IS '是否默认 1表示默认所有显示';
COMMENT ON COLUMN "myapp"."cscp_btn"."ACTION_NAME" IS '接口名称';
COMMENT ON COLUMN "myapp"."cscp_btn"."ACTION_ID" IS 'ACTION_ID';
COMMENT ON COLUMN "myapp"."cscp_btn"."CLASS_NAME" IS '类别名称';
COMMENT ON COLUMN "myapp"."cscp_btn"."ACTION_TYPE" IS '按钮类型';
COMMENT ON COLUMN "myapp"."cscp_btn"."id" IS '主键id';
COMMENT ON COLUMN "myapp"."cscp_btn"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."cscp_btn"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."cscp_btn"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_btn"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."cscp_btn"."update_name" IS '修改人';
COMMENT ON COLUMN "myapp"."cscp_btn"."update_time" IS '跟新时间';
COMMENT ON COLUMN "myapp"."cscp_btn"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_btn"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_btn"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_btn"."deleted" IS '逻辑删除 0：未删除 1：删除';


CREATE  INDEX "INDEX33556802" ON "myapp"."cscp_btn"("id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "INDEX33556803" ON "myapp"."cscp_btn"("FUNCTION_NAME" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."cscp_holiday"
(
    "END_DATE" VARCHAR(10),
    "START_DATE" VARCHAR(10),
    "ID" VARCHAR(100) NOT NULL,
    "TYPE" VARCHAR(10),
    "REMARK" VARCHAR(500),
    "NAME" VARCHAR(100),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_holiday"."END_DATE" IS '结束日期';
COMMENT ON COLUMN "myapp"."cscp_holiday"."START_DATE" IS '开始日期';
COMMENT ON COLUMN "myapp"."cscp_holiday"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."cscp_holiday"."TYPE" IS '类型';
COMMENT ON COLUMN "myapp"."cscp_holiday"."REMARK" IS '备注';
COMMENT ON COLUMN "myapp"."cscp_holiday"."NAME" IS '名称';




CREATE TABLE "myapp"."cscp_information"
(
    "ACCEPTER" VARCHAR(50),
    "DESP_DEPT" VARCHAR(50),
    "DESP_NAME" VARCHAR(100),
    "ACCEPT_NAME" VARCHAR(100),
    "ACCEPT_DEPTNAME" VARCHAR(100),
    "DESPATCHER" VARCHAR(50),
    "SYS_MODULE" VARCHAR(255),
    "ACCEPT_DEPT" VARCHAR(100),
    "DESPATCHER_NAME" VARCHAR(255),
    "READ_STATUS" VARCHAR(50),
    "SUBJECT" VARCHAR(255),
    "ID" VARCHAR(50) NOT NULL,
    "CREATE_TIME" VARCHAR(100),
    "BID" VARCHAR(100),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_information"."ACCEPTER" IS '接收者';
COMMENT ON COLUMN "myapp"."cscp_information"."DESP_NAME" IS '所属机构名称';
COMMENT ON COLUMN "myapp"."cscp_information"."ACCEPT_NAME" IS '接收人名称';
COMMENT ON COLUMN "myapp"."cscp_information"."ACCEPT_DEPTNAME" IS '接收部门名称';
COMMENT ON COLUMN "myapp"."cscp_information"."DESPATCHER" IS '调度人';
COMMENT ON COLUMN "myapp"."cscp_information"."SYS_MODULE" IS '系统模块';
COMMENT ON COLUMN "myapp"."cscp_information"."ACCEPT_DEPT" IS '接收部门';
COMMENT ON COLUMN "myapp"."cscp_information"."DESPATCHER_NAME" IS '发送人名称';
COMMENT ON COLUMN "myapp"."cscp_information"."READ_STATUS" IS '查看状态';
COMMENT ON COLUMN "myapp"."cscp_information"."SUBJECT" IS '主题';
COMMENT ON COLUMN "myapp"."cscp_information"."ID" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_information"."CREATE_TIME" IS '创建时间';




CREATE TABLE "myapp"."cscp_opinion"
(
    "OPINION_TYPE" VARCHAR(1),
    "ID" VARCHAR(64) NOT NULL,
    "OPINION" VARCHAR(255),
    "CREATE_TIME" TIMESTAMP(0),
    "SORT" INT,
    "CREATE_BY" BIGINT,
    "CREATE_NAME" VARCHAR(32),
    "UPDATE_TIME" TIMESTAMP(0),
    "UPDATE_BY" BIGINT,
    "UPDATE_NAME" VARCHAR(32),
    "DEPARTMENT_ID" BIGINT,
    "COMPANY_ID" BIGINT,
    "TENANT_ID" BIGINT,
    "DELETED" INT,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_opinion" IS '系统常用意见';COMMENT ON COLUMN "myapp"."cscp_opinion"."OPINION_TYPE" IS '意见类型（0：个人意见 1:系统意见）';
COMMENT ON COLUMN "myapp"."cscp_opinion"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."cscp_opinion"."OPINION" IS '意见';
COMMENT ON COLUMN "myapp"."cscp_opinion"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_opinion"."SORT" IS '序号';
COMMENT ON COLUMN "myapp"."cscp_opinion"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."cscp_opinion"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."cscp_opinion"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."cscp_opinion"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."cscp_opinion"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."cscp_opinion"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_opinion"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_opinion"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_opinion"."DELETED" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."cscp_out_authorization"
(
    "MODULENAME" VARCHAR(100),
    "UPDATETIME" TIMESTAMP(0),
    "ENDTIME" VARCHAR(50),
    "TASKTODO" VARCHAR(100),
    "AUTHORIZEDNAME" VARCHAR(200),
    "OUTADDRESS" VARCHAR(200),
    "AUTHORIZEDUSERID" VARCHAR(200),
    "STATUS" VARCHAR(10),
    "STARTTIME" VARCHAR(50),
    "MODULES" VARCHAR(200),
    "TECHPROCESS" VARCHAR(100),
    "USERID" VARCHAR(100),
    "USERNAME" VARCHAR(100),
    "CREATEROCESS" VARCHAR(100),
    "ID" VARCHAR(100) NOT NULL,
    "CREATETIME" TIMESTAMP(0),
    "AUTH_TYPE, " VARCHAR(20),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_out_authorization"."MODULENAME" IS '模块名';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."UPDATETIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."TASKTODO" IS '任务待办事项';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."AUTHORIZEDNAME" IS '经授权的';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."OUTADDRESS" IS '其他';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."STARTTIME" IS '开始时间';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."MODULES" IS '模块';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."TECHPROCESS" IS '流程';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."USERID" IS '用户id';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."USERNAME" IS '用户名';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."CREATEROCESS" IS '创建者';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."ID" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."CREATETIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_out_authorization"."AUTH_TYPE" IS '身份验证类型';




CREATE TABLE "myapp"."cscp_proc_base"
(
    "ID" VARCHAR(64) NOT NULL,
    "PROC_SEQ_NO" VARCHAR(200),
    "PROC_TYPE" VARCHAR(64),
    "PROC_DEF_ID" VARCHAR(64),
    "PROC_TYPE_NAME" VARCHAR(200),
    "PROC_TYPE_ID" VARCHAR(64),
    "PROC_END_DEPTNAME" VARCHAR(200),
    "ACT_DEF_UNIQUE_NAME" VARCHAR(500),
    "PROC_END_USERNAME" VARCHAR(200),
    "PROC_END_TIME" VARCHAR(32),
    "MODELKEY" VARCHAR(64),
    "PROC_IS_SHOW" VARCHAR(1),
    "BID" VARCHAR(200),
    "VARI_EXT" TEXT,
    "PROC_END_DEPTID" VARCHAR(64),
    "PROC_INST_ID" VARCHAR(64, ),
"FORM_DEF_ID" VARCHAR(64),
"EXT1" VARCHAR(2000),
"EXT2" VARCHAR(2000),
"EXT3" VARCHAR(2000),
"EXT4" VARCHAR(2000),
"ACT_DEF_UNIQUE_ID" VARCHAR(64),
"EXT5" VARCHAR(2000),
"FORM_DATA_ID" VARCHAR(64),
"PROC_END_USERID" VARCHAR(64),
"table_name" VARCHAR(255),
"create_by" BIGINT,
"create_name" VARCHAR(100),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(64),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"dele, ted" INT,
"title" VARCHAR(2000),
"bpm_status" INT,
"document" INT,
"annex" INT,
"cscp_proc_id" BIGINT,
"company_name" VARCHAR(100),
"department_name" VARCHAR(100),
"root_PROC_INST_ID" VARCHAR(255),
"processing_sheet" INT DEFAULT 0,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_proc_base" IS '流程启动后的基础数据表';COMMENT ON COLUMN "myapp"."cscp_proc_base"."ID" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_SEQ_NO" IS '程序编号';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_TYPE" IS '过程类型';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_DEF_ID" IS '流程定义id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_TYPE_NAME" IS '流程分类名称级显示名称';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_TYPE_ID" IS '进程类型ID';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_END_DEPTNAME" IS '过程结束部门名称';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."ACT_DEF_UNIQUE_NAME" IS '行为定义唯一名称';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_END_USERNAME" IS '过程结束用户名';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_END_TIME" IS '过程结束时间';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."MODELKEY" IS '型号';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."VARI_EXT" IS '变量扩展';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_END_DEPTID" IS '过程结束';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."PROC_INST_ID" IS '流程定义id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."FORM_DEF_ID" IS '表单定义ID';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."table_name" IS '表名';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."update_name" IS '修改人';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."update_time" IS '跟新时间';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."title" IS '业务标题';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."bpm_status" IS '流程状态2办理中 3 完成，其他参考具体文档';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."document" IS '是否正文 1表示有正文 0表示无正文';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."annex" IS '是否附件 1表示有附件 0表示无附件';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."cscp_proc_id" IS '流程模型id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."company_name" IS '单位名称';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."department_name" IS '部门名称';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."root_PROC_INST_ID" IS '父流程定义id';
COMMENT ON COLUMN "myapp"."cscp_proc_base"."processing_sheet" IS '父子流程是否共用处理单1表示共用，0表示共用';


CREATE  INDEX "index_ProcTypeId" ON "myapp"."cscp_proc_base"("PROC_TYPE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "index_ProcId" ON "myapp"."cscp_proc_base"("cscp_proc_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "index_ProcInstId" ON "myapp"."cscp_proc_base"("PROC_INST_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "index_FormDataId" ON "myapp"."cscp_proc_base"("FORM_DATA_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."cscp_proc_group"
(
    "ID" BIGINT NOT NULL,
    "PERMISSION_NAME" VARCHAR(50),
    "SORT" INT,
    "CREATE_TIME" TIMESTAMP(0),
    "CREATE_BY" BIGINT,
    "CREATE_NAME" VARCHAR(32),
    "UPDATE_TIME" TIMESTAMP(0),
    "UPDATE_BY" BIGINT,
    "UPDATE_NAME" VARCHAR(32),
    "DEPARTMENT_ID" BIGINT,
    "COMPANY_ID" BIGINT,
    "TENANT_ID" BIGINT,
    "DELETED" INT,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_proc_group"."ID" IS '主键';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."PERMISSION_NAME" IS '权限名称';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."SORT" IS '排序';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_proc_group"."DELETED" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."cscp_proc_org"
(
    "ID" BIGINT NOT NULL,
    "PERMISSION_GROUP_ID" BIGINT,
    "ORG_ID" BIGINT,
    "ORG_NAME" VARCHAR(50),
    "CREATE_TIME" TIMESTAMP(0),
    "CREATE_BY" BIGINT,
    "CREATE_NAME" VARCHAR(32),
    "UPDATE_TIME" TIMESTAMP(0),
    "UPDATE_BY" BIGINT,
    "UPDATE_NAME" VARCHAR(32),
    "DEPARTMENT_ID" BIGINT,
    "COMPANY_ID" BIGINT,
    "TENANT_ID" BIGINT,
    "DELETED" INT,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_proc_org"."ID" IS '主键';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."PERMISSION_GROUP_ID" IS '权限组id';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."ORG_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."ORG_NAME" IS '单位名称';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_proc_org"."DELETED" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."cscp_proc_permissions"
(
    "APP_ID" VARCHAR(64),
    "CREATE_DATE" VARCHAR(20) NOT NULL,
    "OBJ_TYPE" VARCHAR(10),
    "PROCESS_DEFINITION_KEY_" VARCHAR(64),
    "PERMISSIONS_ID_" VARCHAR(64),
    "ID" VARCHAR(64) NOT NULL,
    "PERMISSIONS_TYPE_" VARCHAR(1),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_proc_permissions"."APP_ID" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_proc_permissions"."CREATE_DATE" IS '创建日期';
COMMENT ON COLUMN "myapp"."cscp_proc_permissions"."OBJ_TYPE" IS '对象类型';
COMMENT ON COLUMN "myapp"."cscp_proc_permissions"."PROCESS_DEFINITION_KEY_" IS '进程\定义\密钥';
COMMENT ON COLUMN "myapp"."cscp_proc_permissions"."PERMISSIONS_ID_" IS '权限ID_';
COMMENT ON COLUMN "myapp"."cscp_proc_permissions"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."cscp_proc_permissions"."PERMISSIONS_TYPE_" IS '权限类型';




CREATE TABLE "myapp"."cscp_proc_task_extend"
(
    "ID" BIGINT NOT NULL,
    "PROCINST_ID" VARCHAR(50),
    "PROCDEF_ID" VARCHAR(50),
    "ACTIVITY_ID" VARCHAR(50),
    "TASK_ID" VARCHAR(50),
    "IN_TASK_ID" VARCHAR(50),
    "IN_ACT_ID" VARCHAR(50),
    "OUT_TASK_ID" VARCHAR(500),
    "OUT_ACT_ID" VARCHAR(500),
    "NODE_TYPE" VARCHAR(20),
    "LIMIT_TIME" VARCHAR(50),
    "USER_ID" VARCHAR(50),
    "DEPT_ID" VARCHAR(50),
    "DEAL_USER_ID" VARCHAR(50),
    "DEAL_DEPT_ID" VARCHAR(50),
    "JUMP_TYPE" VARCHAR(1),
    "BPMN_NODE_TYPE" VARCHAR(25, 5),
"END_TIME" VARCHAR(50),
"create_by" BIGINT,
"create_name" VARCHAR(100),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_proc_task_extend" IS '保存所有处理过的和待处理的节点信息';COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."PROCINST_ID" IS '流程实例id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."PROCDEF_ID" IS '流程定义id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."ACTIVITY_ID" IS '任务的节点id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."TASK_ID" IS '任务id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."IN_TASK_ID" IS '进入的任务ID';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."IN_ACT_ID" IS '进入的节点id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."OUT_TASK_ID" IS '输出任务ID';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."OUT_ACT_ID" IS '输出任务的节点key';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."NODE_TYPE" IS '节点类型3表示多实例节点';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."LIMIT_TIME" IS '时间';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."USER_ID" IS '用户id，当前节点的处理用户id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."DEPT_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."DEAL_USER_ID" IS '处理用户id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."DEAL_DEPT_ID" IS '处理人的单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."JUMP_TYPE" IS '跳转类型';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."BPMN_NODE_TYPE" IS '流程节点的类型';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."END_TIME" IS '任务结束时间';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."update_name" IS '修改人';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."update_time" IS '跟新时间';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_proc_task_extend"."deleted" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."cscp_proc_type"
(
    "TYPE_ID" VARCHAR(200) NOT NULL,
    "APP_CODE" VARCHAR(200) NOT NULL,
    "REMARKS" VARCHAR(4000),
    "TYPE_NAME" VARCHAR(200) NOT NULL,
    "parent_type_name" VARCHAR(200),
    "PARENT_TYPE_ID" VARCHAR(200),
    "create_by" BIGINT,
    "create_name" VARCHAR(100),
    "create_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "update_time" TIMESTAMP(0),
    "department_id" BIGINT,
    "company_id" BIGINT,
    "tenant_id" BIGINT,
    "deleted" INT,
    NOT CLUSTER PRIMARY , KEY("TYPE_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_proc_type" IS '工作流分类表';COMMENT ON COLUMN "myapp"."cscp_proc_type"."TYPE_ID" IS '类型id';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."APP_CODE" IS '代码';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."REMARKS" IS '备注';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."TYPE_NAME" IS '类型名字';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."parent_type_name" IS '父类项的名称';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."PARENT_TYPE_ID" IS '父项类型ID';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."update_name" IS '修改人';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."update_time" IS '跟新时间';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_proc_type"."deleted" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."cscp_proc_user"
(
    "ID" BIGINT NOT NULL,
    "PERMISSION_GROUP_ID" BIGINT,
    "USER_ID" BIGINT,
    "REAL_NAME" VARCHAR(50),
    "SORT" INT,
    "CREATE_TIME" TIMESTAMP(0),
    "CREATE_BY" BIGINT,
    "CREATE_NAME" VARCHAR(32),
    "UPDATE_TIME" TIMESTAMP(0),
    "UPDATE_BY" BIGINT,
    "UPDATE_NAME" VARCHAR(32),
    "DEPARTMENT_ID" BIGINT,
    "COMPANY_ID" BIGINT,
    "TENANT_ID" BIGINT,
    "DELETED" INT,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_proc_user"."ID" IS '主键';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."PERMISSION_GROUP_ID" IS '权限组id';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."USER_ID" IS '用户id';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."REAL_NAME" IS '鐢ㄦ埛鍚嶇О';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."SORT" IS '排序';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_proc_user"."DELETED" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."cscp_proc_viewtask"
(
    "PROC_STATUS" VARCHAR(1),
    "COPY_USER_ID" VARCHAR(64),
    "COPY_OPINION" VARCHAR(2000),
    "CRAFTER_ID" VARCHAR(64),
    "COPY_USER_NAME" VARCHAR(255),
    "CRAFTER_NAME" VARCHAR(500),
    "PROC_INST_ID" VARCHAR(64),
    "CREATE_DEPT" VARCHAR(64),
    "PROC_DEF_ID" VARCHAR(64),
    "COPY_TIME" VARCHAR(20),
    "FORM_DEF_ID" VARCHAR(64),
    "PROC_ORGAN" VARCHAR(64),
    "CREATE_DATE" VARCHAR(100),
    "SEQ_NO" VARCHAR(100),
    "FORM_DATA_ID" VARCHAR(64),
    "PROC_NAME" VARCHAR(255),
    "SU, BJECT" VARCHAR(500),
    "ACT_DEF_ID" VARCHAR(64),
    "ID" VARCHAR(64) NOT NULL,
    "ACT_NAME" VARCHAR(255),
    "COPY_STATUS" VARCHAR(1),
    "COPY_SEND_DEPT" VARCHAR(64),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;





CREATE TABLE "myapp"."cscp_seq_id"
(
    "SEQ_ID" VARCHAR(50) NOT NULL,
    "SEQ_NAME" VARCHAR(50),
    "SEQ_ALIAS" VARCHAR(50),
    "CUR_DATE" VARCHAR(19),
    "SEQ_RULE" VARCHAR(100),
    "RULE_CONF" VARCHAR(500),
    "INIT_VAL" INT,
    "GEN_TYPE" VARCHAR(20),
    "SEQ_LEN" INT,
    "CUR_VAL" INT,
    "STEP" INT,
    "MEMO" VARCHAR(500),
    "IS_DEFAULT" VARCHAR(20),
    "TENANT_ID" VARCHAR(64),
    "CREATE_BY" VARCHAR(64),
    "CREATE_TIME" VARCHAR(19),
    "UPDATE_BY" VARCHAR(64),
    "UPDATE_TIME" VARCHAR(19),
    "RULE_JSON" CLOB,
    NOT CLUSTER,  PRIMARY KEY("SEQ_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."cscp_seq_id"."SEQ_NAME" IS '名称';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."SEQ_ALIAS" IS '别名';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."CUR_DATE" IS '当前日期';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."SEQ_RULE" IS '规则';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."RULE_CONF" IS '规则配置';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."INIT_VAL" IS '初始值';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."GEN_TYPE" IS '生成方式 DAY=每天 WEEK=每周 MONTH=每月YEAR=每年AUTO=一直增长';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."SEQ_LEN" IS '流水号长度';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."CUR_VAL" IS '当前值';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."STEP" IS '步长';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."MEMO" IS '备注';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."IS_DEFAULT" IS '系统缺省 YES NO';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."TENANT_ID" IS '租用用户Id';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."CREATE_BY" IS '创建人ID';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."UPDATE_BY" IS '更新人ID';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."cscp_seq_id"."RULE_JSON" IS '流水号内容生成配置';




CREATE TABLE "myapp"."cscp_user"
(
    "id" BIGINT NOT NULL,
    "login_name" VARCHAR(50),
    "real_name_start" VARCHAR(500),
    "real_name_end" VARCHAR(500),
    "password" VARCHAR(100),
    "real_name" VARCHAR(500),
    "email" VARCHAR(50),
    "mobile_end" VARCHAR(255),
    "mobile_middle" VARCHAR(255),
    "mobile_start" VARCHAR(255),
    "mobile" VARCHAR(255),
    "last_login" TIMESTAMP(0),
    "tenant_id" BIGINT,
    "create_time" TIMESTAMP(0),
    "create_by" BIGINT,
    "create_name" VARCHAR(50),
    "update_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(255),
    "order_by" INT,
    "status" INT DEFAULT 1,
    "display" INT DEFAULT 1,
    "office_phone" VARCHAR(32),
    "statistics" INT DEFAULT 1,
    "deleted" INT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_user" IS '用户详细信息';
COMMENT ON COLUMN "myapp"."cscp_user"."display" IS '用户是否显示,1表示显示，默认显示';
COMMENT ON COLUMN "myapp"."cscp_user"."email" IS '邮箱';
COMMENT ON COLUMN "myapp"."cscp_user"."last_login" IS '最后一次登陆时间';
COMMENT ON COLUMN "myapp"."cscp_user"."mobile" IS '电话';
COMMENT ON COLUMN "myapp"."cscp_user"."mobile_end" IS '手机号码的最后4位';
COMMENT ON COLUMN "myapp"."cscp_user"."mobile_middle" IS '手机号码的中间4位';
COMMENT ON COLUMN "myapp"."cscp_user"."mobile_start" IS '手机号码的开始3位';
COMMENT ON COLUMN "myapp"."cscp_user"."office_phone" IS '办公电话';
COMMENT ON COLUMN "myapp"."cscp_user"."order_by" IS '用户排序';
COMMENT ON COLUMN "myapp"."cscp_user"."password" IS '密码';
COMMENT ON COLUMN "myapp"."cscp_user"."real_name" IS '真实用户姓名';
COMMENT ON COLUMN "myapp"."cscp_user"."real_name_end" IS '姓名的后缀';
COMMENT ON COLUMN "myapp"."cscp_user"."real_name_start" IS '姓名的前缀';
COMMENT ON COLUMN "myapp"."cscp_user"."statistics" IS '是否统计，1表示统计，0表示不统计，默认统计';
COMMENT ON COLUMN "myapp"."cscp_user"."status" IS '用户状态，1表示激活，0表示锁定，默认激活';
COMMENT ON COLUMN "myapp"."cscp_user"."tenant_id" IS '租户id';



CREATE TABLE "myapp"."cscp_user_org"
(
    "id" BIGINT NOT NULL,
    "user_id" BIGINT NOT NULL,
    "org_id" BIGINT NOT NULL,
    "deleted" INT,
    "order_by" INT,
    "default_department" INT,
    "company_id" BIGINT,
    "branch_leader" INT,
    "department_head" INT,
    "post" VARCHAR(100),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_user_org" IS '用户机构表';
COMMENT ON COLUMN "myapp"."cscp_user_org"."branch_leader" IS '是否分管领导(1个部门只能有1个)';
COMMENT ON COLUMN "myapp"."cscp_user_org"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."cscp_user_org"."default_department" IS '是否默认登录账号，同一个账号属于多个单位或者部门时，1表示默认登录账号';
COMMENT ON COLUMN "myapp"."cscp_user_org"."department_head" IS '是否为部门领导(1个部门可以设置多个)';
COMMENT ON COLUMN "myapp"."cscp_user_org"."order_by" IS '排序';
COMMENT ON COLUMN "myapp"."cscp_user_org"."org_id" IS '组织机构id';
COMMENT ON COLUMN "myapp"."cscp_user_org"."post" IS '职务（用于通讯录）及其系统管理';
COMMENT ON COLUMN "myapp"."cscp_user_org"."user_id" IS '用户id';


CREATE  INDEX "index_company_id" ON "myapp"."cscp_user_org"("company_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "index_user_id" ON "myapp"."cscp_user_org"("user_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "index_org_id" ON "myapp"."cscp_user_org"("org_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."cscp_user_work_group"
(
    "id" BIGINT NOT NULL,
    "user_id" BIGINT NOT NULL,
    "group_id" BIGINT NOT NULL,
    "create_by" BIGINT,
    "create_name" VARCHAR(100),
    "create_time" TIMESTAMP(0),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "update_time" TIMESTAMP(0),
    "department_id" BIGINT,
    "company_id" BIGINT,
    "tenant_id" BIGINT,
    "deleted" INT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_user_work_group" IS '用户与工作组的对应关系表';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."group_id" IS 'groupid';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."update_name" IS '修改人';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."update_time" IS '跟新时间';
COMMENT ON COLUMN "myapp"."cscp_user_work_group"."user_id" IS '鐢ㄦ埛id';


CREATE  INDEX "INDEX503420394969200" ON "myapp"."cscp_user_work_group"("user_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "index_groupId" ON "myapp"."cscp_user_work_group"("group_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."cscp_proc"
(
    "ID_" VARCHAR(255) NOT NULL,
    "XML_" CLOB,
    "FORM_URL" VARCHAR(100),
    "ONLINE_FLAG" VARCHAR(1),
    "FORM_TYPE" VARCHAR(10),
    "OPERATE_TYPE" VARCHAR(10),
    "PROCESS_DEFINITION_ID_" VARCHAR(255),
    "PROCESS_DEPLOYMENT_ID_" VARCHAR(255),
    "SEQ_ID" VARCHAR(50),
    "FORM_NAME" VARCHAR(255),
    "TYPE_ID" VARCHAR(255),
    "PROCESS_DEFINITION_KEY_" VARCHAR(255),
    "MAX_VERSION_" INT,
    "SVG_" CLOB,
    "NAME_" VARCHAR(255),
    "FORM_ID" VARCHAR(50),
    "CREATE_TIME" TIMESTAMP(0),
    "UPDATE_TIME" TIMESTAMP(0),
    "create_by" BIGINT,
    "create_name" VARCHAR(100),
    "update_by" BIGINT,
    "update_name" VARCHAR(32),
    "department_id" BIGINT,
    "company_id" BIGINT,
    "tenant_id" BIGINT,
    "deleted" INT,
    "is_permission" TINYINT DEFAULT 0,
    "permission_type" VARCHAR(100),
    "permission_type_values" VARCHAR(500),
    NOT CLUSTER PRIMARY KEY("ID_")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."cscp_proc" IS '流程模型表';
COMMENT ON COLUMN "myapp"."cscp_proc"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."cscp_proc"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."cscp_proc"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."cscp_proc"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."cscp_proc"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."cscp_proc"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."cscp_proc"."FORM_ID" IS '表单id';
COMMENT ON COLUMN "myapp"."cscp_proc"."FORM_NAME" IS '表单名称';
COMMENT ON COLUMN "myapp"."cscp_proc"."FORM_TYPE" IS '表单类型';
COMMENT ON COLUMN "myapp"."cscp_proc"."FORM_URL" IS '表单URL 非内置表单是的表单路径';
COMMENT ON COLUMN "myapp"."cscp_proc"."ID_" IS 'id';
COMMENT ON COLUMN "myapp"."cscp_proc"."is_permission" IS '是否需要授权,0不需要，1需要';
COMMENT ON COLUMN "myapp"."cscp_proc"."MAX_VERSION_" IS '版本号1和0';
COMMENT ON COLUMN "myapp"."cscp_proc"."NAME_" IS '流程名称';
COMMENT ON COLUMN "myapp"."cscp_proc"."ONLINE_FLAG" IS '1表示上线';
COMMENT ON COLUMN "myapp"."cscp_proc"."OPERATE_TYPE" IS '娴佽浆绫诲瀷 1姝ｅ父2锛氱涓夋柟鍙戣捣3锛氬紩鎿?';
COMMENT ON COLUMN "myapp"."cscp_proc"."permission_type" IS '授权的方式，按照组，部门等';
COMMENT ON COLUMN "myapp"."cscp_proc"."permission_type_values" IS '授权的组的集合';
COMMENT ON COLUMN "myapp"."cscp_proc"."PROCESS_DEFINITION_ID_" IS '流程定义的id';
COMMENT ON COLUMN "myapp"."cscp_proc"."PROCESS_DEFINITION_KEY_" IS '流程定义的key';
COMMENT ON COLUMN "myapp"."cscp_proc"."PROCESS_DEPLOYMENT_ID_" IS '流程发布id';
COMMENT ON COLUMN "myapp"."cscp_proc"."SEQ_ID" IS '搴忓彿ID';
COMMENT ON COLUMN "myapp"."cscp_proc"."SVG_" IS 'SVG参数';
COMMENT ON COLUMN "myapp"."cscp_proc"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."cscp_proc"."TYPE_ID" IS '流程类型id';
COMMENT ON COLUMN "myapp"."cscp_proc"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."cscp_proc"."update_name" IS '修改人';
COMMENT ON COLUMN "myapp"."cscp_proc"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."cscp_proc"."XML_" IS 'xml数据';


CREATE  INDEX "index_processDefiniedId" ON "myapp"."cscp_proc"("PROCESS_DEFINITION_ID_" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;




