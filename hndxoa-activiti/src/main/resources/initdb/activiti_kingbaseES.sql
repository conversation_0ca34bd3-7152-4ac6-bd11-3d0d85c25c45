CREATE TABLE "public"."cscp_app" (
    ID varchar(64 char) NOT NULL,
	APP_CODE varchar(64 char) NOT NULL,
	APP_NAME varchar(64 char) NOT NULL,
	SECRET_KEY varchar(100 char) NOT NULL,
	REMARKS varchar(500 char) NULL,
	CONSTRAINT "cscp_app_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_app"."REMARKS" IS '备注';
COMMENT ON COLUMN "public"."cscp_app"."SECRET_KEY" IS '密钥';
COMMENT ON COLUMN "public"."cscp_app"."APP_NAME" IS '名称';
COMMENT ON COLUMN "public"."cscp_app"."APP_CODE" IS '编码';
COMMENT ON COLUMN "public"."cscp_app"."ID" IS 'id';
COMMENT ON TABLE "public"."cscp_app" IS '第三方应用的工作流分类的顶级';


CREATE TABLE "public"."cscp_audit_content" (
    ID varchar(64 char) NOT NULL,
	DELEGATOR_NAME varchar(500 char) NULL,
	DELEGATOR_ID varchar(200 char) NULL,
	AUDITOR_NAME varchar(500 char) NULL,
	PROC_DEF_ID varchar(64 char) NULL,
	PROC_INST_ID varchar(64 char) NULL,
	ACT_NAME varchar(200 char) NULL,
	ACT_ID varchar(64 char) NULL,
	AUDIT_CONTENT varchar(1000 char) NULL,
	AUDITOR_ID varchar(200 char) NULL,
	CLIENT_TYPE varchar(20 char) NULL,
	AUDIT_STATUS varchar(1 char) NULL,
	AUDIT_TIME varchar(20 char) NULL,
	COMMENTS_FORM_ID varchar(500 char) NULL,
	"business_id" varchar(20 char) NULL,
	"root_proc_inst_id" varchar(20 char) NULL,
	CONSTRAINT "cscp_audit_content_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_audit_content"."root_proc_inst_id" IS '父节点的流程实例id';
COMMENT ON COLUMN "public"."cscp_audit_content"."business_id" IS '业务表的id';
COMMENT ON COLUMN "public"."cscp_audit_content"."COMMENTS_FORM_ID" IS '意见栏的表单id';
COMMENT ON COLUMN "public"."cscp_audit_content"."AUDIT_TIME" IS '审核时间';
COMMENT ON COLUMN "public"."cscp_audit_content"."AUDIT_STATUS" IS '审核状态0正常，1回退 2是移交，3跳转';
COMMENT ON COLUMN "public"."cscp_audit_content"."CLIENT_TYPE" IS '审核客户端';
COMMENT ON COLUMN "public"."cscp_audit_content"."AUDITOR_ID" IS '审核员id';
COMMENT ON COLUMN "public"."cscp_audit_content"."AUDIT_CONTENT" IS '审核内容';
COMMENT ON COLUMN "public"."cscp_audit_content"."ACT_ID" IS '流程步骤';
COMMENT ON COLUMN "public"."cscp_audit_content"."ACT_NAME" IS '审核名称';
COMMENT ON COLUMN "public"."cscp_audit_content"."PROC_INST_ID" IS '流程实例id';
COMMENT ON COLUMN "public"."cscp_audit_content"."PROC_DEF_ID" IS '流程定义id';;
COMMENT ON COLUMN "public"."cscp_audit_content"."AUDITOR_NAME" IS '审核人';
COMMENT ON COLUMN "public"."cscp_audit_content"."DELEGATOR_ID" IS '委托人id';
COMMENT ON COLUMN "public"."cscp_audit_content"."DELEGATOR_NAME" IS '委托人姓名';
COMMENT ON COLUMN "public"."cscp_audit_content"."ID" IS 'id';
COMMENT ON TABLE "public"."cscp_audit_content" IS '审核意见表';
CREATE INDEX cscp_audit_content_index_businessId ON cscp_audit_content USING btree (business_id);
CREATE INDEX cscp_audit_content_index_procInstId ON cscp_audit_content USING btree (PROC_INST_ID);


CREATE TABLE "public"."cscp_audit_log" (
    PROCESS_STATUS varchar(1 char) NULL,
	DELEGATOR_ID varchar(200 char) NULL,
	AUDITOR_NAME varchar(500 char) NULL,
	PROC_INST_ID varchar(64 char) NULL,
	ACT_ID varchar(64 char) NULL,
	PROC_DEF_ID varchar(64 char) NULL,
	OPERATE_TYPE varchar(50 char) NULL,
	AUDIT_CONTENT varchar(1000 char) NULL,
	AUDITOR_ID varchar(200 char) NULL,
	EXT1 varchar(100 char) NULL,
	EXT2 varchar(100 char) NULL,
	DELEGATOR_NAME varchar(500 char) NULL,
	CLIENT_TYPE varchar(1 char) NULL,
	EXT3 varchar(100 char) NULL,
	EXT4 varchar(100 char) NULL,
	EXT5 varchar(100 char) NULL,
	AUDIT_STATUS varchar(1 char) NULL,
	TITLE varchar(500 char) NULL,
	ID varchar(64 char) NOT NULL,
	ACT_NAME varchar(200 char) NULL,
	AUDIT_TIME varchar(20 char) NULL,
	"to_node_name" varchar(50 char) NULL
);
COMMENT ON COLUMN "public"."cscp_audit_log"."AUDIT_TIME" IS '审核时间';
COMMENT ON COLUMN "public"."cscp_audit_log"."ACT_NAME" IS '行为人姓名';
COMMENT ON COLUMN "public"."cscp_audit_log"."ID" IS 'id';
COMMENT ON COLUMN "public"."cscp_audit_log"."TITLE" IS '标题';
COMMENT ON COLUMN "public"."cscp_audit_log"."AUDIT_STATUS" IS '审核状态';
COMMENT ON COLUMN "public"."cscp_audit_log"."DELEGATOR_NAME" IS '授权人姓名';
COMMENT ON COLUMN "public"."cscp_audit_log"."AUDITOR_ID" IS '审核人id';
COMMENT ON COLUMN "public"."cscp_audit_log"."AUDIT_CONTENT" IS '审核内容';
COMMENT ON COLUMN "public"."cscp_audit_log"."OPERATE_TYPE" IS '操作类型';
COMMENT ON COLUMN "public"."cscp_audit_log"."PROC_DEF_ID" IS '过程定义id';
COMMENT ON COLUMN "public"."cscp_audit_log"."ACT_ID" IS '行为id';
COMMENT ON COLUMN "public"."cscp_audit_log"."PROC_INST_ID" IS '过程id';
COMMENT ON COLUMN "public"."cscp_audit_log"."AUDITOR_NAME" IS '审核人名称';
COMMENT ON COLUMN "public"."cscp_audit_log"."DELEGATOR_ID" IS '委托人id';
COMMENT ON COLUMN "public"."cscp_audit_log"."PROCESS_STATUS" IS '进程状态';
COMMENT ON TABLE "public"."cscp_audit_log" IS '审核日志表';


CREATE TABLE "public"."cscp_btn" (
                                     DISPLAY_ORDER int4 NULL,
                                     FUNCTION_NAME varchar(200 char) NULL,
	DESCRIPTION varchar(500 char) NULL,
	SUB_TYPE varchar(200 char) NULL,
	IS_DEFAULT varchar(1 char) NULL,
	ACTION_NAME varchar(200 char) NOT NULL,
	ACTION_ID varchar(200 char) NULL,
	CLASS_NAME varchar(200 char) NULL,
	ACTION_TYPE varchar(200 char) NULL,
	"id" int8 NOT NULL,
	"create_by" int8 NULL,
	"create_name" varchar(100 char) NULL,
	"create_time" timestamp NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "cscp_btn_INDEX_FUNCTION_NAME" UNIQUE (FUNCTION_NAME) ENABLE VALIDATE,
	CONSTRAINT "cscp_btn_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_btn"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_btn"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."cscp_btn"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."cscp_btn"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."cscp_btn"."update_time" IS '跟新时间';
COMMENT ON COLUMN "public"."cscp_btn"."update_name" IS '修改人';
COMMENT ON COLUMN "public"."cscp_btn"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."cscp_btn"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_btn"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."cscp_btn"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."cscp_btn"."id" IS '主键id';
COMMENT ON COLUMN "public"."cscp_btn"."ACTION_TYPE" IS '按钮类型';
COMMENT ON COLUMN "public"."cscp_btn"."CLASS_NAME" IS '类别名称';
COMMENT ON COLUMN "public"."cscp_btn"."ACTION_ID" IS 'ACTION_ID';
COMMENT ON COLUMN "public"."cscp_btn"."ACTION_NAME" IS '接口名称';
COMMENT ON COLUMN "public"."cscp_btn"."IS_DEFAULT" IS '是否默认 1表示默认所有显示';
COMMENT ON COLUMN "public"."cscp_btn"."SUB_TYPE" IS '子类型';
COMMENT ON COLUMN "public"."cscp_btn"."DESCRIPTION" IS '描述';
COMMENT ON COLUMN "public"."cscp_btn"."FUNCTION_NAME" IS '函数名称';
COMMENT ON COLUMN "public"."cscp_btn"."DISPLAY_ORDER" IS '按钮显示顺序';
COMMENT ON TABLE "public"."cscp_btn" IS '表单中按钮的表';


CREATE TABLE "public"."cscp_holiday" (
    END_DATE varchar(10 char) NULL,
	START_DATE varchar(10 char) NULL,
	ID varchar(100 char) NOT NULL,
	"TYPE" varchar(10 char) NULL,
	REMARK varchar(500 char) NULL,
	NAME varchar(100 char) NULL,
	CONSTRAINT "cscp_holiday_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_holiday"."NAME" IS '名称';
COMMENT ON COLUMN "public"."cscp_holiday"."REMARK" IS '备注';
COMMENT ON COLUMN "public"."cscp_holiday"."TYPE" IS '类型';
COMMENT ON COLUMN "public"."cscp_holiday"."ID" IS '主键id';
COMMENT ON COLUMN "public"."cscp_holiday"."START_DATE" IS '开始日期';
COMMENT ON COLUMN "public"."cscp_holiday"."END_DATE" IS '结束日期';


CREATE TABLE "public"."cscp_information" (
    ACCEPTER varchar(50 char) NULL,
	DESP_DEPT varchar(50 char) NULL,
	DESP_NAME varchar(100 char) NULL,
	ACCEPT_NAME varchar(100 char) NULL,
	ACCEPT_DEPTNAME varchar(100 char) NULL,
	DESPATCHER varchar(50 char) NULL,
	SYS_MODULE varchar(255 char) NULL,
	ACCEPT_DEPT varchar(100 char) NULL,
	DESPATCHER_NAME varchar(255 char) NULL,
	READ_STATUS varchar(50 char) NULL,
	SUBJECT varchar(255 char) NULL,
	ID varchar(50 char) NOT NULL,
	CREATE_TIME varchar(100 char) NULL,
	BID varchar(100 char) NULL,
	CONSTRAINT "cscp_information_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_information"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_information"."ID" IS 'id';
COMMENT ON COLUMN "public"."cscp_information"."SUBJECT" IS '主题';
COMMENT ON COLUMN "public"."cscp_information"."READ_STATUS" IS '查看状态';
COMMENT ON COLUMN "public"."cscp_information"."DESPATCHER_NAME" IS '发送人名称';
COMMENT ON COLUMN "public"."cscp_information"."ACCEPT_DEPT" IS '接收部门';
COMMENT ON COLUMN "public"."cscp_information"."SYS_MODULE" IS '系统模块';
COMMENT ON COLUMN "public"."cscp_information"."DESPATCHER" IS '调度人';
COMMENT ON COLUMN "public"."cscp_information"."ACCEPT_DEPTNAME" IS '接收部门名称';
COMMENT ON COLUMN "public"."cscp_information"."ACCEPT_NAME" IS '接收人名称';
COMMENT ON COLUMN "public"."cscp_information"."DESP_NAME" IS '所属机构名称';
COMMENT ON COLUMN "public"."cscp_information"."ACCEPTER" IS '接收者';


CREATE TABLE "public"."cscp_opinion" (
    OPINION_TYPE varchar(1 char) NULL,
	ID varchar(64 char) NOT NULL,
	OPINION varchar(255 char) NULL,
	CREATE_TIME timestamp NULL,
	SORT int4 NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int4 NULL,
	CONSTRAINT "cscp_opinion_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_opinion"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_opinion"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."cscp_opinion"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."cscp_opinion"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."cscp_opinion"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."cscp_opinion"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."cscp_opinion"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."cscp_opinion"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."cscp_opinion"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."cscp_opinion"."SORT" IS '序号';
COMMENT ON COLUMN "public"."cscp_opinion"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_opinion"."OPINION" IS '意见';
COMMENT ON COLUMN "public"."cscp_opinion"."ID" IS '主键id';
COMMENT ON COLUMN "public"."cscp_opinion"."OPINION_TYPE" IS '意见类型（0：个人意见 1:系统意见）';
COMMENT ON TABLE "public"."cscp_opinion" IS '系统常用意见';


CREATE TABLE "public"."cscp_out_authorization" (
    MODULENAME varchar(100 char) NULL,
	UPDATETIME timestamp(6) NULL,
	ENDTIME varchar(50 char) NULL,
	TASKTODO varchar(100 char) NULL,
	AUTHORIZEDNAME varchar(200 char) NULL,
	OUTADDRESS varchar(200 char) NULL,
	AUTHORIZEDUSERID varchar(200 char) NULL,
	STATUS varchar(10 char) NULL,
	STARTTIME varchar(50 char) NULL,
	MODULES varchar(200 char) NULL,
	TECHPROCESS varchar(100 char) NULL,
	USERID varchar(100 char) NULL,
	USERNAME varchar(100 char) NULL,
	CREATEROCESS varchar(100 char) NULL,
	ID varchar(100 char) NOT NULL,
	CREATETIME timestamp(6) NULL,
	AUTH_TYPE varchar(20 char) NULL,
	CONSTRAINT "cscp_out_authorization_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_out_authorization"."AUTH_TYPE" IS '身份验证类型';
COMMENT ON COLUMN "public"."cscp_out_authorization"."CREATETIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_out_authorization"."ID" IS 'id';
COMMENT ON COLUMN "public"."cscp_out_authorization"."CREATEROCESS" IS '创建者';
COMMENT ON COLUMN "public"."cscp_out_authorization"."USERNAME" IS '用户名';
COMMENT ON COLUMN "public"."cscp_out_authorization"."USERID" IS '用户id';
COMMENT ON COLUMN "public"."cscp_out_authorization"."TECHPROCESS" IS '流程';
COMMENT ON COLUMN "public"."cscp_out_authorization"."MODULES" IS '模块';
COMMENT ON COLUMN "public"."cscp_out_authorization"."STARTTIME" IS '开始时间';
COMMENT ON COLUMN "public"."cscp_out_authorization"."OUTADDRESS" IS '其他';
COMMENT ON COLUMN "public"."cscp_out_authorization"."AUTHORIZEDNAME" IS '经授权的';
COMMENT ON COLUMN "public"."cscp_out_authorization"."TASKTODO" IS '任务待办事项';
COMMENT ON COLUMN "public"."cscp_out_authorization"."UPDATETIME" IS '更新时间';
COMMENT ON COLUMN "public"."cscp_out_authorization"."MODULENAME" IS '模块名';


CREATE TABLE "public"."cscp_proc_base" (
    ID varchar(64 char) NOT NULL,
	PROC_SEQ_NO varchar(200 char) NULL,
	PROC_TYPE varchar(64 char) NULL,
	PROC_DEF_ID varchar(64 char) NULL,
	PROC_TYPE_NAME varchar(200 char) NULL,
	PROC_TYPE_ID varchar(64 char) NULL,
	PROC_END_DEPTNAME varchar(200 char) NULL,
	ACT_DEF_UNIQUE_NAME varchar(500 char) NULL,
	PROC_END_USERNAME varchar(200 char) NULL,
	PROC_END_TIME varchar(32 char) NULL,
	MODELKEY varchar(64 char) NULL,
	PROC_IS_SHOW varchar(1 char) NULL,
	BID varchar(200 char) NULL,
	VARI_EXT text NULL,
	PROC_END_DEPTID varchar(64 char) NULL,
	PROC_INST_ID varchar(64 char) NULL,
	FORM_DEF_ID varchar(64 char) NULL,
	EXT1 varchar(2000 char) NULL,
	EXT2 varchar(2000 char) NULL,
	EXT3 varchar(2000 char) NULL,
	EXT4 varchar(2000 char) NULL,
	ACT_DEF_UNIQUE_ID varchar(64 char) NULL,
	EXT5 varchar(2000 char) NULL,
	FORM_DATA_ID varchar(64 char) NULL,
	PROC_END_USERID varchar(64 char) NULL,
	"table_name" varchar(255 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(100 char) NULL,
	"create_time" timestamp NULL,
	"update_by" int8 NULL,
	"update_name" varchar(64 char) NULL,
	"update_time" timestamp NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	"title" varchar(2000 char) NULL,
	"bpm_status" int4 NULL,
	"document" int4 NULL,
	"annex" int4 NULL,
	"cscp_proc_id" int8 NULL,
	"company_name" varchar(100 char) NULL,
	"department_name" varchar(100 char) NULL,
	"root_PROC_INST_ID" varchar(255 char) NULL,
	"processing_sheet" int4 NULL DEFAULT 0,
	CONSTRAINT "cscp_proc_base_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc_base"."processing_sheet" IS '父子流程是否共用处理单1表示共用，0表示共用';
COMMENT ON COLUMN "public"."cscp_proc_base"."root_PROC_INST_ID" IS '父流程定义id';
COMMENT ON COLUMN "public"."cscp_proc_base"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."cscp_proc_base"."company_name" IS '单位名称';
COMMENT ON COLUMN "public"."cscp_proc_base"."cscp_proc_id" IS '流程模型id';
COMMENT ON COLUMN "public"."cscp_proc_base"."annex" IS '是否附件 1表示有附件 0表示无附件';
COMMENT ON COLUMN "public"."cscp_proc_base"."document" IS '是否正文 1表示有正文 0表示无正文';
COMMENT ON COLUMN "public"."cscp_proc_base"."bpm_status" IS '流程状态2办理中 3 完成，其他参考具体文档';
COMMENT ON COLUMN "public"."cscp_proc_base"."title" IS '业务标题';
COMMENT ON COLUMN "public"."cscp_proc_base"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_proc_base"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."cscp_proc_base"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_base"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."cscp_proc_base"."update_time" IS '跟新时间';
COMMENT ON COLUMN "public"."cscp_proc_base"."update_name" IS '修改人';
COMMENT ON COLUMN "public"."cscp_proc_base"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."cscp_proc_base"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_proc_base"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."cscp_proc_base"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."cscp_proc_base"."table_name" IS '表名';
COMMENT ON COLUMN "public"."cscp_proc_base"."FORM_DEF_ID" IS '表单定义ID';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_INST_ID" IS '流程定义id';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_END_DEPTID" IS '过程结束';
COMMENT ON COLUMN "public"."cscp_proc_base"."VARI_EXT" IS '变量扩展';
COMMENT ON COLUMN "public"."cscp_proc_base"."MODELKEY" IS '型号';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_END_TIME" IS '过程结束时间';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_END_USERNAME" IS '过程结束用户名';
COMMENT ON COLUMN "public"."cscp_proc_base"."ACT_DEF_UNIQUE_NAME" IS '行为定义唯一名称';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_END_DEPTNAME" IS '过程结束部门名称';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_TYPE_ID" IS '进程类型ID';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_TYPE_NAME" IS '流程分类名称级显示名称';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_DEF_ID" IS '流程定义id';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_TYPE" IS '过程类型';
COMMENT ON COLUMN "public"."cscp_proc_base"."PROC_SEQ_NO" IS '程序编号';
COMMENT ON COLUMN "public"."cscp_proc_base"."ID" IS 'id';
COMMENT ON TABLE "public"."cscp_proc_base" IS '流程启动后的基础数据表';
CREATE INDEX cscp_proc_base_index_FormDataId ON cscp_proc_base USING btree (FORM_DATA_ID);
CREATE INDEX cscp_proc_base_index_ProcId ON cscp_proc_base USING btree (cscp_proc_id);
CREATE INDEX cscp_proc_base_index_ProcInstId ON cscp_proc_base USING btree (PROC_INST_ID);
CREATE INDEX cscp_proc_base_index_ProcTypeId ON cscp_proc_base USING btree (PROC_TYPE_ID);


CREATE TABLE "public"."cscp_proc_group" (
                                            ID int8 NOT NULL,
                                            PERMISSION_NAME varchar(50 char) NULL,
	SORT int4 NULL,
	CREATE_TIME timestamp NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int4 NULL,
	CONSTRAINT "cscp_proc_group_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc_group"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_proc_group"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."cscp_proc_group"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_group"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."cscp_proc_group"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."cscp_proc_group"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."cscp_proc_group"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."cscp_proc_group"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."cscp_proc_group"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."cscp_proc_group"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_proc_group"."SORT" IS '排序';
COMMENT ON COLUMN "public"."cscp_proc_group"."PERMISSION_NAME" IS '权限名称';
COMMENT ON COLUMN "public"."cscp_proc_group"."ID" IS '主键';


CREATE TABLE "public"."cscp_proc_org" (
                                          ID int8 NOT NULL,
                                          PERMISSION_GROUP_ID int8 NULL,
                                          ORG_ID int8 NULL,
                                          ORG_NAME varchar(50 char) NULL,
	CREATE_TIME timestamp NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int4 NULL,
	CONSTRAINT "cscp_proc_org_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc_org"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_proc_org"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."cscp_proc_org"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_org"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."cscp_proc_org"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."cscp_proc_org"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."cscp_proc_org"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."cscp_proc_org"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."cscp_proc_org"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."cscp_proc_org"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_proc_org"."ORG_NAME" IS '单位名称';
COMMENT ON COLUMN "public"."cscp_proc_org"."ORG_ID" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_org"."PERMISSION_GROUP_ID" IS '权限组id';
COMMENT ON COLUMN "public"."cscp_proc_org"."ID" IS '主键';


CREATE TABLE "public"."cscp_proc_permissions" (
    APP_ID varchar(64 char) NULL,
	CREATE_DATE varchar(20 char) NOT NULL,
	OBJ_TYPE varchar(10 char) NULL,
	PROCESS_DEFINITION_KEY_ varchar(64 char) NULL,
	PERMISSIONS_ID_ varchar(64 char) NULL,
	ID varchar(64 char) NOT NULL,
	PERMISSIONS_TYPE_ varchar(1 char) NULL,
	CONSTRAINT "cscp_proc_permissions_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc_permissions"."PERMISSIONS_TYPE_" IS '权限类型';
COMMENT ON COLUMN "public"."cscp_proc_permissions"."ID" IS '主键id';
COMMENT ON COLUMN "public"."cscp_proc_permissions"."PERMISSIONS_ID_" IS '权限ID_';
COMMENT ON COLUMN "public"."cscp_proc_permissions"."PROCESS_DEFINITION_KEY_" IS '进程\定义\密钥';
COMMENT ON COLUMN "public"."cscp_proc_permissions"."OBJ_TYPE" IS '对象类型';
COMMENT ON COLUMN "public"."cscp_proc_permissions"."CREATE_DATE" IS '创建日期';
COMMENT ON COLUMN "public"."cscp_proc_permissions"."APP_ID" IS 'id';


CREATE TABLE "public"."cscp_proc_task_extend" (
                                                  ID int8 NOT NULL,
                                                  PROCINST_ID varchar(50 char) NULL,
	PROCDEF_ID varchar(50 char) NULL,
	ACTIVITY_ID varchar(50 char) NULL,
	TASK_ID varchar(50 char) NULL,
	IN_TASK_ID varchar(50 char) NULL,
	IN_ACT_ID varchar(50 char) NULL,
	OUT_TASK_ID varchar(500 char) NULL,
	OUT_ACT_ID varchar(500 char) NULL,
	NODE_TYPE varchar(20 char) NULL,
	LIMIT_TIME varchar(50 char) NULL,
	USER_ID varchar(50 char) NULL,
	DEPT_ID varchar(50 char) NULL,
	DEAL_USER_ID varchar(50 char) NULL,
	DEAL_DEPT_ID varchar(50 char) NULL,
	JUMP_TYPE varchar(1 char) NULL,
	BPMN_NODE_TYPE varchar(255 char) NULL,
	END_TIME varchar(50 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(100 char) NULL,
	"create_time" timestamp NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "cscp_proc_task_extend_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."update_time" IS '跟新时间';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."update_name" IS '修改人';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."END_TIME" IS '任务结束时间';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."BPMN_NODE_TYPE" IS '流程节点的类型';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."JUMP_TYPE" IS '跳转类型';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."DEAL_DEPT_ID" IS '处理人的单位id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."DEAL_USER_ID" IS '处理用户id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."DEPT_ID" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."USER_ID" IS '用户id，当前节点的处理用户id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."LIMIT_TIME" IS '时间';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."NODE_TYPE" IS '节点类型3表示多实例节点';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."OUT_ACT_ID" IS '输出任务的节点key';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."OUT_TASK_ID" IS '输出任务ID';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."IN_ACT_ID" IS '进入的节点id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."IN_TASK_ID" IS '进入的任务ID';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."TASK_ID" IS '任务id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."ACTIVITY_ID" IS '任务的节点id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."PROCDEF_ID" IS '流程定义id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."PROCINST_ID" IS '流程实例id';
COMMENT ON COLUMN "public"."cscp_proc_task_extend"."ID" IS '主键id';
COMMENT ON TABLE "public"."cscp_proc_task_extend" IS '保存所有处理过的和待处理的节点信息';


CREATE TABLE "public"."cscp_proc_type" (
    TYPE_ID varchar(200 char) NOT NULL,
	APP_CODE varchar(200 char) NOT NULL,
	REMARKS varchar(4000 char) NULL,
	TYPE_NAME varchar(200 char) NOT NULL,
	"parent_type_name" varchar(200 char) NULL,
	PARENT_TYPE_ID varchar(200 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(100 char) NULL,
	"create_time" timestamp NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "cscp_proc_type_PRIMARY" PRIMARY KEY (TYPE_ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc_type"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_proc_type"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."cscp_proc_type"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_type"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."cscp_proc_type"."update_time" IS '跟新时间';
COMMENT ON COLUMN "public"."cscp_proc_type"."update_name" IS '修改人';
COMMENT ON COLUMN "public"."cscp_proc_type"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."cscp_proc_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_proc_type"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."cscp_proc_type"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."cscp_proc_type"."PARENT_TYPE_ID" IS '父项类型ID';
COMMENT ON COLUMN "public"."cscp_proc_type"."parent_type_name" IS '父类项的名称';
COMMENT ON COLUMN "public"."cscp_proc_type"."TYPE_NAME" IS '类型名字';
COMMENT ON COLUMN "public"."cscp_proc_type"."REMARKS" IS '备注';
COMMENT ON COLUMN "public"."cscp_proc_type"."APP_CODE" IS '代码';
COMMENT ON COLUMN "public"."cscp_proc_type"."TYPE_ID" IS '类型id';
COMMENT ON TABLE "public"."cscp_proc_type" IS '工作流分类表';


CREATE TABLE "public"."cscp_proc_user" (
                                           ID int8 NOT NULL,
                                           PERMISSION_GROUP_ID int8 NULL,
                                           USER_ID int8 NULL,
                                           REAL_NAME varchar(50 char) NULL,
	SORT int4 NULL,
	CREATE_TIME timestamp NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int4 NULL,
	CONSTRAINT "cscp_proc_user_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc_user"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_proc_user"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."cscp_proc_user"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc_user"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."cscp_proc_user"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."cscp_proc_user"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."cscp_proc_user"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."cscp_proc_user"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."cscp_proc_user"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."cscp_proc_user"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_proc_user"."SORT" IS '排序';
COMMENT ON COLUMN "public"."cscp_proc_user"."REAL_NAME" IS '鐢ㄦ埛鍚嶇О';
COMMENT ON COLUMN "public"."cscp_proc_user"."USER_ID" IS '用户id';
COMMENT ON COLUMN "public"."cscp_proc_user"."PERMISSION_GROUP_ID" IS '权限组id';
COMMENT ON COLUMN "public"."cscp_proc_user"."ID" IS '主键';


CREATE TABLE "public"."cscp_proc_viewtask" (
    PROC_STATUS varchar(1 char) NULL,
	COPY_USER_ID varchar(64 char) NULL,
	COPY_OPINION varchar(2000 char) NULL,
	CRAFTER_ID varchar(64 char) NULL,
	COPY_USER_NAME varchar(255 char) NULL,
	CRAFTER_NAME varchar(500 char) NULL,
	PROC_INST_ID varchar(64 char) NULL,
	CREATE_DEPT varchar(64 char) NULL,
	PROC_DEF_ID varchar(64 char) NULL,
	COPY_TIME varchar(20 char) NULL,
	FORM_DEF_ID varchar(64 char) NULL,
	PROC_ORGAN varchar(64 char) NULL,
	CREATE_DATE varchar(100 char) NULL,
	SEQ_NO varchar(100 char) NULL,
	FORM_DATA_ID varchar(64 char) NULL,
	PROC_NAME varchar(255 char) NULL,
	SUBJECT varchar(500 char) NULL,
	ACT_DEF_ID varchar(64 char) NULL,
	ID varchar(64 char) NOT NULL,
	ACT_NAME varchar(255 char) NULL,
	COPY_STATUS varchar(1 char) NULL,
	COPY_SEND_DEPT varchar(64 char) NULL,
	CONSTRAINT "cscp_proc_viewtask_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);


CREATE TABLE "public"."cscp_seq_id" (
    SEQ_ID varchar(50 char) NOT NULL,
	SEQ_NAME varchar(50 char) NULL,
	SEQ_ALIAS varchar(50 char) NULL,
	CUR_DATE varchar(19 char) NULL,
	SEQ_RULE varchar(100 char) NULL,
	RULE_CONF varchar(500 char) NULL,
	INIT_VAL int4 NULL,
	GEN_TYPE varchar(20 char) NULL,
	SEQ_LEN int4 NULL,
	CUR_VAL int4 NULL,
	STEP int4 NULL,
	MEMO varchar(500 char) NULL,
	IS_DEFAULT varchar(20 char) NULL,
	TENANT_ID varchar(64 char) NULL,
	CREATE_BY varchar(64 char) NULL,
	CREATE_TIME varchar(19 char) NULL,
	UPDATE_BY varchar(64 char) NULL,
	UPDATE_TIME varchar(19 char) NULL,
	RULE_JSON text NULL,
	CONSTRAINT "cscp_seq_id_PRIMARY" PRIMARY KEY (SEQ_ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_seq_id"."RULE_JSON" IS '流水号内容生成配置';
COMMENT ON COLUMN "public"."cscp_seq_id"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."cscp_seq_id"."UPDATE_BY" IS '更新人ID';
COMMENT ON COLUMN "public"."cscp_seq_id"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_seq_id"."CREATE_BY" IS '创建人ID';
COMMENT ON COLUMN "public"."cscp_seq_id"."TENANT_ID" IS '租用用户Id';
COMMENT ON COLUMN "public"."cscp_seq_id"."IS_DEFAULT" IS '系统缺省 YES NO';
COMMENT ON COLUMN "public"."cscp_seq_id"."MEMO" IS '备注';
COMMENT ON COLUMN "public"."cscp_seq_id"."STEP" IS '步长';
COMMENT ON COLUMN "public"."cscp_seq_id"."CUR_VAL" IS '当前值';
COMMENT ON COLUMN "public"."cscp_seq_id"."SEQ_LEN" IS '流水号长度';
COMMENT ON COLUMN "public"."cscp_seq_id"."GEN_TYPE" IS '生成方式 DAY=每天 WEEK=每周 MONTH=每月YEAR=每年AUTO=一直增长';
COMMENT ON COLUMN "public"."cscp_seq_id"."INIT_VAL" IS '初始值';
COMMENT ON COLUMN "public"."cscp_seq_id"."RULE_CONF" IS '规则配置';
COMMENT ON COLUMN "public"."cscp_seq_id"."SEQ_RULE" IS '规则';
COMMENT ON COLUMN "public"."cscp_seq_id"."CUR_DATE" IS '当前日期';
COMMENT ON COLUMN "public"."cscp_seq_id"."SEQ_ALIAS" IS '别名';
COMMENT ON COLUMN "public"."cscp_seq_id"."SEQ_NAME" IS '名称';


CREATE TABLE "public"."cscp_user" (
                                      "id" int8 NOT NULL,
                                      "login_name" varchar(50 char) NULL,
	"real_name_start" varchar(500 char) NULL,
	"real_name_end" varchar(500 char) NULL,
	"password" varchar(100 char) NULL,
	"real_name" varchar(500 char) NULL,
	"email" varchar(50 char) NULL,
	"mobile_end" varchar(255 char) NULL,
	"mobile_middle" varchar(255 char) NULL,
	"mobile_start" varchar(255 char) NULL,
	"mobile" varchar(255 char) NULL,
	"last_login" timestamp NULL,
	"tenant_id" int8 NULL,
	"create_time" timestamp NULL,
	"create_by" int8 NULL,
	"create_name" varchar(50 char) NULL,
	"update_time" timestamp NULL,
	"update_by" int8 NULL,
	"update_name" varchar(255 char) NULL,
	"order_by" int4 NULL,
	"status" int4 NULL DEFAULT 1,
	"display" int4 NULL DEFAULT 1,
	"office_phone" varchar(32 char) NULL,
	"statistics" int4 NULL DEFAULT 1,
	"deleted" int4 NULL,
	CONSTRAINT "cscp_user_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_user"."statistics" IS '是否统计，1表示统计，0表示不统计，默认统计';
COMMENT ON COLUMN "public"."cscp_user"."office_phone" IS '办公电话';
COMMENT ON COLUMN "public"."cscp_user"."display" IS '用户是否显示,1表示显示，默认显示';
COMMENT ON COLUMN "public"."cscp_user"."status" IS '用户状态，1表示激活，0表示锁定，默认激活';
COMMENT ON COLUMN "public"."cscp_user"."order_by" IS '用户排序';
COMMENT ON COLUMN "public"."cscp_user"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."cscp_user"."last_login" IS '最后一次登陆时间';
COMMENT ON COLUMN "public"."cscp_user"."mobile" IS '电话';
COMMENT ON COLUMN "public"."cscp_user"."mobile_start" IS '手机号码的开始3位';
COMMENT ON COLUMN "public"."cscp_user"."mobile_middle" IS '手机号码的中间4位';
COMMENT ON COLUMN "public"."cscp_user"."mobile_end" IS '手机号码的最后4位';
COMMENT ON COLUMN "public"."cscp_user"."email" IS '邮箱';
COMMENT ON COLUMN "public"."cscp_user"."real_name" IS '真实用户姓名';
COMMENT ON COLUMN "public"."cscp_user"."password" IS '密码';
COMMENT ON COLUMN "public"."cscp_user"."real_name_end" IS '姓名的后缀';
COMMENT ON COLUMN "public"."cscp_user"."real_name_start" IS '姓名的前缀';
COMMENT ON TABLE "public"."cscp_user" IS '用户详细信息';


CREATE TABLE "public"."cscp_user_org" (
                                          "id" int8 NOT NULL,
                                          "user_id" int8 NOT NULL,
                                          "org_id" int8 NOT NULL,
                                          "deleted" int4 NULL,
                                          "order_by" int4 NULL,
                                          "default_department" int4 NULL,
                                          "company_id" int8 NULL,
                                          "branch_leader" int4 NULL,
                                          "department_head" int4 NULL,
                                          "post" varchar(100 char) NULL,
	CONSTRAINT "cscp_user_org_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_user_org"."post" IS '职务（用于通讯录）及其系统管理';
COMMENT ON COLUMN "public"."cscp_user_org"."department_head" IS '是否为部门领导(1个部门可以设置多个)';
COMMENT ON COLUMN "public"."cscp_user_org"."branch_leader" IS '是否分管领导(1个部门只能有1个)';
COMMENT ON COLUMN "public"."cscp_user_org"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."cscp_user_org"."default_department" IS '是否默认登录账号，同一个账号属于多个单位或者部门时，1表示默认登录账号';
COMMENT ON COLUMN "public"."cscp_user_org"."order_by" IS '排序';
COMMENT ON COLUMN "public"."cscp_user_org"."org_id" IS '组织机构id';
COMMENT ON COLUMN "public"."cscp_user_org"."user_id" IS '用户id';
COMMENT ON TABLE "public"."cscp_user_org" IS '用户机构表';
CREATE INDEX cscp_user_org_index_company_id ON cscp_user_org USING btree (company_id);
CREATE INDEX cscp_user_org_index_org_id ON cscp_user_org USING btree (org_id);
CREATE INDEX cscp_user_org_index_user_id ON cscp_user_org USING btree (user_id);


CREATE TABLE "public"."cscp_user_work_group" (
                                                 "id" int8 NOT NULL,
                                                 "user_id" int8 NOT NULL,
                                                 "group_id" int8 NOT NULL,
                                                 "create_by" int8 NULL,
                                                 "create_name" varchar(100 char) NULL,
	"create_time" timestamp NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "cscp_user_work_group_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_user_work_group"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_user_work_group"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."cscp_user_work_group"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."cscp_user_work_group"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."cscp_user_work_group"."update_time" IS '跟新时间';
COMMENT ON COLUMN "public"."cscp_user_work_group"."update_name" IS '修改人';
COMMENT ON COLUMN "public"."cscp_user_work_group"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."cscp_user_work_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_user_work_group"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."cscp_user_work_group"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."cscp_user_work_group"."group_id" IS 'groupid';
COMMENT ON COLUMN "public"."cscp_user_work_group"."user_id" IS '鐢ㄦ埛id';
COMMENT ON TABLE "public"."cscp_user_work_group" IS '用户与工作组的对应关系表';
CREATE INDEX cscp_user_work_group_index_groupId ON cscp_user_work_group USING btree (group_id);
CREATE INDEX cscp_user_work_group_index_userId ON cscp_user_work_group USING btree (user_id);


CREATE TABLE "public"."cscp_proc" (
    ID_ varchar(255 char) NOT NULL,
	XML_ text NULL,
	FORM_URL varchar(100 char) NULL,
	ONLINE_FLAG varchar(1 char) NULL,
	FORM_TYPE varchar(10 char) NULL,
	OPERATE_TYPE varchar(10 char) NULL,
	PROCESS_DEFINITION_ID_ varchar(255 char) NULL,
	PROCESS_DEPLOYMENT_ID_ varchar(255 char) NULL,
	SEQ_ID varchar(50 char) NULL,
	FORM_NAME varchar(255 char) NULL,
	TYPE_ID varchar(255 char) NULL,
	PROCESS_DEFINITION_KEY_ varchar(255 char) NULL,
	MAX_VERSION_ int4 NULL,
	SVG_ text NULL,
	NAME_ varchar(255 char) NULL,
	FORM_ID varchar(50 char) NULL,
	CREATE_TIME timestamp NULL,
	UPDATE_TIME timestamp NULL,
	"create_by" int8 NULL,
	"create_name" varchar(100 char) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	"is_permission" int2 NULL DEFAULT 0,
	"permission_type" varchar(100 char) NULL,
	"permission_type_values" varchar(500 char) NULL,
	CONSTRAINT "cscp_proc_PRIMARY" PRIMARY KEY (ID_) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."cscp_proc"."permission_type_values" IS '授权的组的集合';
COMMENT ON COLUMN "public"."cscp_proc"."permission_type" IS '授权的方式，按照组，部门等';
COMMENT ON COLUMN "public"."cscp_proc"."is_permission" IS '是否需要授权,0不需要，1需要';
COMMENT ON COLUMN "public"."cscp_proc"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."cscp_proc"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."cscp_proc"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."cscp_proc"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."cscp_proc"."update_name" IS '修改人';
COMMENT ON COLUMN "public"."cscp_proc"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."cscp_proc"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."cscp_proc"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."cscp_proc"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."cscp_proc"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."cscp_proc"."FORM_ID" IS '表单id';
COMMENT ON COLUMN "public"."cscp_proc"."NAME_" IS '流程名称';
COMMENT ON COLUMN "public"."cscp_proc"."SVG_" IS 'SVG参数';
COMMENT ON COLUMN "public"."cscp_proc"."MAX_VERSION_" IS '版本号1和0';
COMMENT ON COLUMN "public"."cscp_proc"."PROCESS_DEFINITION_KEY_" IS '流程定义的key';
COMMENT ON COLUMN "public"."cscp_proc"."TYPE_ID" IS '流程类型id';
COMMENT ON COLUMN "public"."cscp_proc"."FORM_NAME" IS '表单名称';
COMMENT ON COLUMN "public"."cscp_proc"."SEQ_ID" IS '搴忓彿ID';
COMMENT ON COLUMN "public"."cscp_proc"."PROCESS_DEPLOYMENT_ID_" IS '流程发布id';
COMMENT ON COLUMN "public"."cscp_proc"."PROCESS_DEFINITION_ID_" IS '流程定义的id';
COMMENT ON COLUMN "public"."cscp_proc"."OPERATE_TYPE" IS '娴佽浆绫诲瀷 1姝ｅ父2锛氱涓夋柟鍙戣捣3锛氬紩鎿?';
COMMENT ON COLUMN "public"."cscp_proc"."FORM_TYPE" IS '表单类型';
COMMENT ON COLUMN "public"."cscp_proc"."ONLINE_FLAG" IS '1表示上线';
COMMENT ON COLUMN "public"."cscp_proc"."FORM_URL" IS '表单URL 非内置表单是的表单路径';
COMMENT ON COLUMN "public"."cscp_proc"."XML_" IS 'xml数据';
COMMENT ON COLUMN "public"."cscp_proc"."ID_" IS 'id';
COMMENT ON TABLE "public"."cscp_proc" IS '流程模型表';
CREATE INDEX cscp_proc_index_processDefiniedId ON cscp_proc USING btree (PROCESS_DEFINITION_ID_);




