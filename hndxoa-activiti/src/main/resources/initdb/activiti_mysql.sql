CREATE TABLE `cscp_app` (
                            `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                            `APP_CODE` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
                            `APP_NAME` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
                            `SECRET_KEY` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密钥',
                            `REMARKS` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='第三方应用的工作流分类的顶级';

CREATE TABLE `cscp_audit_content` (
                                      `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                                      `DELEGATOR_NAME` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '委托人姓名',
                                      `DELEGATOR_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '委托人id',
                                      `AUDITOR_NAME` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核人',
                                      `PROC_DEF_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '\r\n流程定义id',
                                      `PROC_INST_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程实例id',
                                      `ACT_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核名称',
                                      `ACT_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程步骤',
                                      `AUDIT_CONTENT` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核内容',
                                      `AUDITOR_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核员id',
                                      `CLIENT_TYPE` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核客户端',
                                      `AUDIT_STATUS` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核状态0正常，1回退 2是移交，3跳转',
                                      `AUDIT_TIME` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核时间',
                                      `COMMENTS_FORM_ID` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '意见栏的表单id',
                                      `business_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '业务表的id',
                                      `root_proc_inst_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父节点的流程实例id',
                                      PRIMARY KEY (`ID`) USING BTREE,
                                      KEY `index_procInstId` (`PROC_INST_ID`) USING BTREE,
                                      KEY `index_businessId` (`business_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='审核意见表';

CREATE TABLE `cscp_audit_log` (
                                  `PROCESS_STATUS` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '进程状态',
                                  `DELEGATOR_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '委托人id',
                                  `AUDITOR_NAME` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核人名称',
                                  `PROC_INST_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '过程id',
                                  `ACT_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行为id',
                                  `PROC_DEF_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '过程定义id',
                                  `OPERATE_TYPE` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作类型',
                                  `AUDIT_CONTENT` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核内容',
                                  `AUDITOR_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核人id',
                                  `EXT1` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT2` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `DELEGATOR_NAME` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '授权人姓名',
                                  `CLIENT_TYPE` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT3` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT4` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT5` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `AUDIT_STATUS` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核状态',
                                  `TITLE` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                                  `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                                  `ACT_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行为人姓名',
                                  `AUDIT_TIME` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核时间',
                                  `to_node_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='审核日志表';

CREATE TABLE `cscp_btn` (
                            `DISPLAY_ORDER` int DEFAULT NULL COMMENT '按钮显示顺序',
                            `FUNCTION_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '函数名称',
                            `DESCRIPTION` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述',
                            `SUB_TYPE` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '子类型',
                            `IS_DEFAULT` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否默认 1表示默认所有显示',
                            `ACTION_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '接口名称',
                            `ACTION_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'ACTION_ID',
                            `CLASS_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类别名称',
                            `ACTION_TYPE` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '按钮类型',
                            `id` bigint NOT NULL COMMENT '主键id',
                            `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                            `create_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                            `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                            `update_time` datetime DEFAULT NULL COMMENT '跟新时间',
                            `department_id` bigint DEFAULT NULL COMMENT '部门id',
                            `company_id` bigint DEFAULT NULL COMMENT '单位id',
                            `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                            `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE KEY `INDEX_FUNCTION_NAME` (`FUNCTION_NAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='表单中按钮的表';

CREATE TABLE `cscp_holiday` (
                                `END_DATE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '结束日期',
                                `START_DATE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '开始日期',
                                `ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
                                `TYPE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类型',
                                `REMARK` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                `NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '名称',
                                PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_information` (
                                    `ACCEPTER` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '接收者',
                                    `DESP_DEPT` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                    `DESP_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属机构名称',
                                    `ACCEPT_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '接收人名称',
                                    `ACCEPT_DEPTNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '接收部门名称',
                                    `DESPATCHER` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '调度人',
                                    `SYS_MODULE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '系统模块',
                                    `ACCEPT_DEPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '接收部门',
                                    `DESPATCHER_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发送人名称',
                                    `READ_STATUS` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '查看状态',
                                    `SUBJECT` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主题',
                                    `ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                                    `CREATE_TIME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建时间',
                                    `BID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                    PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_opinion` (
                                `OPINION_TYPE` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '意见类型（0：个人意见 1:系统意见）',
                                `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
                                `OPINION` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '意见',
                                `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                `SORT` int DEFAULT NULL COMMENT '序号',
                                `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人id',
                                `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
                                `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
                                `UPDATE_BY` bigint DEFAULT NULL COMMENT '修改人id',
                                `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                `DEPARTMENT_ID` bigint DEFAULT NULL COMMENT '部门id',
                                `COMPANY_ID` bigint DEFAULT NULL COMMENT '单位id',
                                `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id',
                                `DELETED` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统常用意见';

CREATE TABLE `cscp_out_authorization` (
                                          `MODULENAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模块名',
                                          `UPDATETIME` datetime(6) DEFAULT NULL COMMENT '更新时间',
                                          `ENDTIME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                          `TASKTODO` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务待办事项',
                                          `AUTHORIZEDNAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '经授权的',
                                          `OUTADDRESS` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '其他',
                                          `AUTHORIZEDUSERID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                          `STATUS` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                          `STARTTIME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '开始时间',
                                          `MODULES` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模块',
                                          `TECHPROCESS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程',
                                          `USERID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户id',
                                          `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户名',
                                          `CREATEROCESS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建者',
                                          `ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                                          `CREATETIME` datetime(6) DEFAULT NULL COMMENT '创建时间',
                                          `AUTH_TYPE` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '身份验证类型',
                                          PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_proc_base` (
                                  `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                                  `PROC_SEQ_NO` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '程序编号',
                                  `PROC_TYPE` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '过程类型',
                                  `PROC_DEF_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程定义id',
                                  `PROC_TYPE_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程分类名称级显示名称',
                                  `PROC_TYPE_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '进程类型ID',
                                  `PROC_END_DEPTNAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '过程结束部门名称',
                                  `ACT_DEF_UNIQUE_NAME` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行为定义唯一名称',
                                  `PROC_END_USERNAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '过程结束用户名',
                                  `PROC_END_TIME` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '过程结束时间',
                                  `MODELKEY` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '型号',
                                  `PROC_IS_SHOW` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `BID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `VARI_EXT` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '变量扩展',
                                  `PROC_END_DEPTID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '过程结束',
                                  `PROC_INST_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程定义id',
                                  `FORM_DEF_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单定义ID',
                                  `EXT1` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT2` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT3` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT4` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `ACT_DEF_UNIQUE_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `EXT5` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `FORM_DATA_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `PROC_END_USERID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                  `table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表名',
                                  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                  `create_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                                  `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                  `update_time` datetime DEFAULT NULL COMMENT '跟新时间',
                                  `department_id` bigint DEFAULT NULL COMMENT '部门id',
                                  `company_id` bigint DEFAULT NULL COMMENT '单位id',
                                  `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                  `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                  `title` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '业务标题',
                                  `bpm_status` int DEFAULT NULL COMMENT '流程状态2办理中 3 完成，其他参考具体文档',
                                  `document` int DEFAULT NULL COMMENT '是否正文 1表示有正文 0表示无正文',
                                  `annex` int DEFAULT NULL COMMENT '是否附件 1表示有附件 0表示无附件',
                                  `cscp_proc_id` bigint DEFAULT NULL COMMENT '流程模型id',
                                  `company_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                  `department_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
                                  `root_PROC_INST_ID` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父流程定义id',
                                  `processing_sheet` int DEFAULT '0' COMMENT '父子流程是否共用处理单1表示共用，0表示共用',
                                  PRIMARY KEY (`ID`) USING BTREE,
                                  KEY `index_ProcId` (`cscp_proc_id`) USING BTREE,
                                  KEY `index_ProcInstId` (`PROC_INST_ID`) USING BTREE,
                                  KEY `index_FormDataId` (`FORM_DATA_ID`) USING BTREE,
                                  KEY `index_ProcTypeId` (`PROC_TYPE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='流程启动后的基础数据表';

CREATE TABLE `cscp_proc_group` (
                                   `ID` bigint NOT NULL COMMENT '主键',
                                   `PERMISSION_NAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '权限名称',
                                   `SORT` int DEFAULT NULL COMMENT '排序',
                                   `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                   `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人id',
                                   `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
                                   `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
                                   `UPDATE_BY` bigint DEFAULT NULL COMMENT '修改人id',
                                   `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                   `DEPARTMENT_ID` bigint DEFAULT NULL COMMENT '部门id',
                                   `COMPANY_ID` bigint DEFAULT NULL COMMENT '单位id',
                                   `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id',
                                   `DELETED` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                   PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_proc_org` (
                                 `ID` bigint NOT NULL COMMENT '主键',
                                 `PERMISSION_GROUP_ID` bigint DEFAULT NULL COMMENT '权限组id',
                                 `ORG_ID` bigint DEFAULT NULL COMMENT '单位id',
                                 `ORG_NAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                 `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                 `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人id',
                                 `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
                                 `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
                                 `UPDATE_BY` bigint DEFAULT NULL COMMENT '修改人id',
                                 `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                 `DEPARTMENT_ID` bigint DEFAULT NULL COMMENT '部门id',
                                 `COMPANY_ID` bigint DEFAULT NULL COMMENT '单位id',
                                 `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id',
                                 `DELETED` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                 PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_proc_permissions` (
                                         `APP_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'id',
                                         `CREATE_DATE` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建日期',
                                         `OBJ_TYPE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '对象类型',
                                         `PROCESS_DEFINITION_KEY_` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '进程\\定义\\密钥',
                                         `PERMISSIONS_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '权限ID_',
                                         `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
                                         `PERMISSIONS_TYPE_` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '权限类型',
                                         PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_proc_task_extend` (
                                         `ID` bigint NOT NULL COMMENT '主键id',
                                         `PROCINST_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程实例id',
                                         `PROCDEF_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程定义id',
                                         `ACTIVITY_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务的节点id',
                                         `TASK_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务id',
                                         `IN_TASK_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '进入的任务ID',
                                         `IN_ACT_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '进入的节点id',
                                         `OUT_TASK_ID` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '输出任务ID',
                                         `OUT_ACT_ID` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '输出任务的节点key',
                                         `NODE_TYPE` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '节点类型3表示多实例节点',
                                         `LIMIT_TIME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '时间',
                                         `USER_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户id，当前节点的处理用户id',
                                         `DEPT_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位id',
                                         `DEAL_USER_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '处理用户id',
                                         `DEAL_DEPT_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '处理人的单位id',
                                         `JUMP_TYPE` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '跳转类型',
                                         `BPMN_NODE_TYPE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程节点的类型',
                                         `END_TIME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务结束时间',
                                         `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                         `create_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                                         `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '跟新时间',
                                         `department_id` bigint DEFAULT NULL COMMENT '部门id',
                                         `company_id` bigint DEFAULT NULL COMMENT '单位id',
                                         `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                         `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                         PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='保存所有处理过的和待处理的节点信息';

CREATE TABLE `cscp_proc_type` (
                                  `TYPE_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型id',
                                  `APP_CODE` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代码',
                                  `REMARKS` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                  `TYPE_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型名字',
                                  `parent_type_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父类项的名称',
                                  `PARENT_TYPE_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父项类型ID',
                                  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                  `create_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                                  `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                  `update_time` datetime DEFAULT NULL COMMENT '跟新时间',
                                  `department_id` bigint DEFAULT NULL COMMENT '部门id',
                                  `company_id` bigint DEFAULT NULL COMMENT '单位id',
                                  `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                  `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                  PRIMARY KEY (`TYPE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='工作流分类表';

CREATE TABLE `cscp_proc_user` (
                                  `ID` bigint NOT NULL COMMENT '主键',
                                  `PERMISSION_GROUP_ID` bigint DEFAULT NULL COMMENT '权限组id',
                                  `USER_ID` bigint DEFAULT NULL COMMENT '用户id',
                                  `REAL_NAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '鐢ㄦ埛鍚嶇О',
                                  `SORT` int DEFAULT NULL COMMENT '排序',
                                  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                  `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人id',
                                  `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
                                  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
                                  `UPDATE_BY` bigint DEFAULT NULL COMMENT '修改人id',
                                  `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                  `DEPARTMENT_ID` bigint DEFAULT NULL COMMENT '部门id',
                                  `COMPANY_ID` bigint DEFAULT NULL COMMENT '单位id',
                                  `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id',
                                  `DELETED` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_proc_viewtask` (
                                      `PROC_STATUS` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `COPY_USER_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `COPY_OPINION` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `CRAFTER_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `COPY_USER_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `CRAFTER_NAME` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `PROC_INST_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `CREATE_DEPT` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `PROC_DEF_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `COPY_TIME` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `FORM_DEF_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `PROC_ORGAN` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `CREATE_DATE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `SEQ_NO` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `FORM_DATA_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `PROC_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `SUBJECT` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `ACT_DEF_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                      `ACT_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `COPY_STATUS` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      `COPY_SEND_DEPT` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                      PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_seq_id` (
                               `SEQ_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                               `SEQ_NAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '名称',
                               `SEQ_ALIAS` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '别名',
                               `CUR_DATE` varchar(19) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '当前日期',
                               `SEQ_RULE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规则',
                               `RULE_CONF` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规则配置',
                               `INIT_VAL` int DEFAULT NULL COMMENT '初始值',
                               `GEN_TYPE` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '生成方式 DAY=每天 WEEK=每周 MONTH=每月YEAR=每年AUTO=一直增长',
                               `SEQ_LEN` int DEFAULT NULL COMMENT '流水号长度',
                               `CUR_VAL` int DEFAULT NULL COMMENT '当前值',
                               `STEP` int DEFAULT NULL COMMENT '步长',
                               `MEMO` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                               `IS_DEFAULT` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '系统缺省 YES NO',
                               `TENANT_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '租用用户Id',
                               `CREATE_BY` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人ID',
                               `CREATE_TIME` varchar(19) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建时间',
                               `UPDATE_BY` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人ID',
                               `UPDATE_TIME` varchar(19) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新时间',
                               `RULE_JSON` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '流水号内容生成配置',
                               PRIMARY KEY (`SEQ_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `cscp_user` (
                             `id` bigint NOT NULL,
                             `login_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                             `real_name_start` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '姓名的前缀',
                             `real_name_end` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '姓名的后缀',
                             `password` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '密码',
                             `real_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '真实用户姓名',
                             `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '邮箱',
                             `mobile_end` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手机号码的最后4位',
                             `mobile_middle` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手机号码的中间4位',
                             `mobile_start` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手机号码的开始3位',
                             `mobile` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '电话',
                             `last_login` timestamp NULL DEFAULT NULL COMMENT '最后一次登陆时间',
                             `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                             `create_time` timestamp NULL DEFAULT NULL,
                             `create_by` bigint DEFAULT NULL,
                             `create_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                             `update_time` timestamp NULL DEFAULT NULL,
                             `update_by` bigint DEFAULT NULL,
                             `update_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                             `order_by` int DEFAULT NULL COMMENT '用户排序',
                             `status` int DEFAULT '1' COMMENT '用户状态，1表示激活，0表示锁定，默认激活',
                             `display` int DEFAULT '1' COMMENT '用户是否显示,1表示显示，默认显示',
                             `office_phone` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '办公电话',
                             `statistics` int DEFAULT '1' COMMENT '是否统计，1表示统计，0表示不统计，默认统计',
                             `deleted` int DEFAULT NULL,
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户详细信息';

CREATE TABLE `cscp_user_org` (
                                 `id` bigint NOT NULL,
                                 `user_id` bigint NOT NULL COMMENT '用户id',
                                 `org_id` bigint NOT NULL COMMENT '组织机构id',
                                 `deleted` int DEFAULT NULL,
                                 `order_by` int DEFAULT NULL COMMENT '排序',
                                 `default_department` int DEFAULT NULL COMMENT '是否默认登录账号，同一个账号属于多个单位或者部门时，1表示默认登录账号',
                                 `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                 `branch_leader` int DEFAULT NULL COMMENT '是否分管领导(1个部门只能有1个)',
                                 `department_head` int DEFAULT NULL COMMENT '是否为部门领导(1个部门可以设置多个)',
                                 `post` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '职务（用于通讯录）及其系统管理',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `index_user_id` (`user_id`) USING BTREE,
                                 KEY `index_org_id` (`org_id`) USING BTREE,
                                 KEY `index_company_id` (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户机构表';

CREATE TABLE `cscp_user_work_group` (
                                        `id` bigint NOT NULL,
                                        `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛id',
                                        `group_id` bigint NOT NULL COMMENT 'groupid',
                                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                        `create_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                                        `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '跟新时间',
                                        `department_id` bigint DEFAULT NULL COMMENT '部门id',
                                        `company_id` bigint DEFAULT NULL COMMENT '单位id',
                                        `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                        `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `index_userId` (`user_id`) USING BTREE,
                                        KEY `index_groupId` (`group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户与工作组的对应关系表';

CREATE TABLE `cscp_proc` (
                             `ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                             `XML_` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'xml数据',
                             `FORM_URL` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单URL 非内置表单是的表单路径',
                             `ONLINE_FLAG` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '1表示上线',
                             `FORM_TYPE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单类型',
                             `OPERATE_TYPE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '娴佽浆绫诲瀷 1姝ｅ父2锛氱涓夋柟鍙戣捣3锛氬紩鎿?',
                             `PROCESS_DEFINITION_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程定义的id',
                             `PROCESS_DEPLOYMENT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程发布id',
                             `SEQ_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '搴忓彿ID',
                             `FORM_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单名称',
                             `TYPE_ID` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程类型id',
                             `PROCESS_DEFINITION_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程定义的key',
                             `MAX_VERSION_` int DEFAULT NULL COMMENT '版本号1和0',
                             `SVG_` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'SVG参数',
                             `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程名称',
                             `FORM_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单id',
                             `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                             `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                             `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                             `create_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                             `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                             `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                             `department_id` bigint DEFAULT NULL COMMENT '部门id',
                             `company_id` bigint DEFAULT NULL COMMENT '单位id',
                             `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                             `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                             `is_permission` tinyint DEFAULT '0' COMMENT '是否需要授权,0不需要，1需要',
                             `permission_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '授权的方式，按照组，部门等',
                             `permission_type_values` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '授权的组的集合',
                             PRIMARY KEY (`ID_`) USING BTREE,
                             KEY `index_processDefiniedId` (`PROCESS_DEFINITION_ID_`) USING BTREE COMMENT '流程定义id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='流程模型表';
