package com.ctsi.wysignimage.controller;
import cn.hutool.core.codec.Base64Decoder;
import com.ctsi.business.domain.CscpAuditContent;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.wysignimage.entity.CscpProcWySignimage;
import com.ctsi.wysignimage.entity.dto.CscpProcWySignimageDTO;
import com.ctsi.wysignimage.service.ICscpProcWySignimageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;

import javax.websocket.server.PathParam;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpProcWySignimage")
@Api(value = "保存处理单的手写签批", tags = "保存处理单的手写签批接口")
public class CscpProcWySignimageController extends BaseController {

    private static final String ENTITY_NAME = "cscpProcWySignimage";

    @Autowired
    private ICscpProcWySignimageService cscpProcWySignimageService;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.cscpProcWySignimage.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增保存处理单的手写签批数据")
   // @PreAuthorize("@permissionService.hasPermi('cscp.cscpProcWySignimage.add')")
    public ResultVO<CscpProcWySignimageDTO> create(@RequestBody CscpProcWySignimageDTO cscpProcWySignimageDTO)  {
        String signImage = cscpProcWySignimageDTO.getSignImage();
        if (StringUtils.isNotEmpty(signImage)){
            signImage = signImage.replace("data:image/png;base64,","");
            byte[] bytes = Base64Decoder.decode(signImage);
            String image = fileStoreTemplateService.createFileUrl(FileBasePathName.SIGN_IMAGE,".png");
            fileStoreTemplateService.uploadFile(image,bytes);
            cscpProcWySignimageDTO.setSignImage(image);
        }
        CscpProcWySignimageDTO result = cscpProcWySignimageService.create(cscpProcWySignimageDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.cscpProcWySignimage.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新保存处理单的手写签批数据")
   // @PreAuthorize("@permissionService.hasPermi('cscp.cscpProcWySignimage.update')")
    public ResultVO update(@RequestBody CscpProcWySignimageDTO cscpProcWySignimageDTO) {
	    Assert.notNull(cscpProcWySignimageDTO.getId(), "general.IdNotNull");
        int count = cscpProcWySignimageService.update(cscpProcWySignimageDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除保存处理单的手写签批数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.cscpProcWySignimage.delete)", notes = "传入参数")
   // @PreAuthorize("@permissionService.hasPermi('cscp.cscpProcWySignimage.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpProcWySignimageService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        CscpProcWySignimageDTO cscpProcWySignimageDTO = cscpProcWySignimageService.findOne(id);
        return ResultVO.success(cscpProcWySignimageDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryCscpProcWySignimagePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<CscpProcWySignimageDTO>> queryCscpProcWySignimagePage(CscpProcWySignimageDTO cscpProcWySignimageDTO, BasePageForm basePageForm) {
        return ResultVO.success(cscpProcWySignimageService.queryListPage(cscpProcWySignimageDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryCscpProcWySignimage")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<CscpProcWySignimageDTO>> queryCscpProcWySignimage(CscpProcWySignimageDTO cscpProcWySignimageDTO) {
       List<CscpProcWySignimageDTO> list = cscpProcWySignimageService.queryList(cscpProcWySignimageDTO);
       return ResultVO.success(new ResResult<CscpProcWySignimageDTO>(list));
   }



    /**
     * 根据流程实例id查询所有的表单的手写签批的图片
     */
    @GetMapping("/queryCscpProcWySignimageByprocInstId")
    @ApiOperation(value = "根据流程实例id查询所有的表单的手写签批的图片base64", notes = "传入参数")
    public ResultVO<List<String>> queryCscpProcWySignimageByprocInstId(@RequestParam Long procInstId ) {
        List<String> list = cscpProcWySignimageService.queryCscpProcWySignimageByprocInstId(procInstId);
        return ResultVO.success(list);
    }

}
