package com.ctsi.wysignimage.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 保存处理单的手写签批
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CscpProcWySignimageDTO对象", description="保存处理单的手写签批")
public class CscpProcWySignimageDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private Long procInstId;

    /**
     * 任务节点id
     */
    @ApiModelProperty(value = "任务节点id")
    private Long taskId;

    /**
     * 网页签批的路径
     */
    @ApiModelProperty(value = "网页签批的路径")
    private String signImage;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private Long businessId;
}
