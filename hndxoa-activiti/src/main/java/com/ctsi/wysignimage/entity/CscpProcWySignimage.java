package com.ctsi.wysignimage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 保存处理单的手写签批
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cscp_proc_wy_signimage")
@ApiModel(value="CscpProcWySignimage对象", description="保存处理单的手写签批")
public class CscpProcWySignimage extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private Long procInstId;

    /**
     * 任务节点id
     */
    @ApiModelProperty(value = "任务节点id")
    private Long taskId;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private Long businessId;


    /**
     * 网页签批的路径
     */
    @ApiModelProperty(value = "网页签批的路径")
    private String signImage;


}
