package com.ctsi.wysignimage.service.impl;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.wysignimage.entity.CscpProcWySignimage;
import com.ctsi.wysignimage.entity.dto.CscpProcWySignimageDTO;
import com.ctsi.wysignimage.mapper.CscpProcWySignimageMapper;
import com.ctsi.wysignimage.service.ICscpProcWySignimageService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 保存处理单的手写签批 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Slf4j
@Service
public class CscpProcWySignimageServiceImpl extends SysBaseServiceImpl<CscpProcWySignimageMapper, CscpProcWySignimage> implements ICscpProcWySignimageService {

    @Autowired
    private CscpProcWySignimageMapper cscpProcWySignimageMapper;
    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<CscpProcWySignimageDTO> queryListPage(CscpProcWySignimageDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<CscpProcWySignimage> queryWrapper = new LambdaQueryWrapper();

        IPage<CscpProcWySignimage> pageData = cscpProcWySignimageMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<CscpProcWySignimageDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, CscpProcWySignimageDTO.class));

        return new PageResult<CscpProcWySignimageDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<CscpProcWySignimageDTO> queryList(CscpProcWySignimageDTO entityDTO) {
        LambdaQueryWrapper<CscpProcWySignimage> queryWrapper = new LambdaQueryWrapper();
        List<CscpProcWySignimage> listData = cscpProcWySignimageMapper.selectList(queryWrapper);
        List<CscpProcWySignimageDTO> CscpProcWySignimageDTOList = ListCopyUtil.copy(listData, CscpProcWySignimageDTO.class);
        return CscpProcWySignimageDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public CscpProcWySignimageDTO findOne(Long id) {
        CscpProcWySignimage cscpProcWySignimage = cscpProcWySignimageMapper.selectById(id);
        return BeanConvertUtils.copyProperties(cscpProcWySignimage, CscpProcWySignimageDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CscpProcWySignimageDTO create(CscpProcWySignimageDTO entityDTO) {
        CscpProcWySignimage cscpProcWySignimage = BeanConvertUtils.copyProperties(entityDTO, CscpProcWySignimage.class);
        save(cscpProcWySignimage);
        return BeanConvertUtils.copyProperties(cscpProcWySignimage, CscpProcWySignimageDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(CscpProcWySignimageDTO entity) {
        CscpProcWySignimage cscpProcWySignimage = BeanConvertUtils.copyProperties(entity, CscpProcWySignimage.class);
        return cscpProcWySignimageMapper.updateById(cscpProcWySignimage);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return cscpProcWySignimageMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param CscpProcWySignimageId
     * @return
     */
    @Override
    public boolean existByCscpProcWySignimageId(Long CscpProcWySignimageId) {
        if (CscpProcWySignimageId != null) {
            LambdaQueryWrapper<CscpProcWySignimage> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpProcWySignimage::getId, CscpProcWySignimageId);
            List<CscpProcWySignimage> result = cscpProcWySignimageMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<CscpProcWySignimageDTO> dataList) {
        List<CscpProcWySignimage> result = ListCopyUtil.copy(dataList, CscpProcWySignimage.class);
        return saveBatch(result);
    }

    @Override
    public List<String> queryCscpProcWySignimageByprocInstId(Long procInstId) {
        LambdaQueryWrapper<CscpProcWySignimage> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpProcWySignimage::getProcInstId, procInstId);

        List<CscpProcWySignimage> result = cscpProcWySignimageMapper.selectListNoAdd(queryWrapper);
        List<String> list = new ArrayList<>();

        result.forEach(i -> {
            String path = i.getSignImage();
            if (StringUtils.isNotEmpty(path)) {
                try {
                    byte[] bytes = fileStoreTemplateService.downloadFile(path);
                    if (bytes != null) {
                        String base64 = Base64Encoder.encode(bytes);
                        list.add(base64);
                    }
                } catch (Exception e) {

                }
            }
        });

        return list;
    }


}
