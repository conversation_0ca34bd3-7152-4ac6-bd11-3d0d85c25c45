package com.ctsi.wysignimage.service;

import com.ctsi.wysignimage.entity.dto.CscpProcWySignimageDTO;
import com.ctsi.wysignimage.entity.CscpProcWySignimage;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 保存处理单的手写签批 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface ICscpProcWySignimageService extends SysBaseServiceI<CscpProcWySignimage> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<CscpProcWySignimageDTO> queryListPage(CscpProcWySignimageDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<CscpProcWySignimageDTO> queryList(CscpProcWySignimageDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    CscpProcWySignimageDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    CscpProcWySignimageDTO create(CscpProcWySignimageDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(CscpProcWySignimageDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByCscpProcWySignimageId
     * @param code
     * @return
     */
    boolean existByCscpProcWySignimageId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<CscpProcWySignimageDTO> dataList);

    /**
     * 根据业务id查询所有的表单的手写签批的图片
     */
    List<String> queryCscpProcWySignimageByprocInstId(Long procInstId);
}
