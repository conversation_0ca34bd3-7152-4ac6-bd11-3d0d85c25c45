package com.ctsi.procgroup.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.mybatisplus.sort.SortMapper;
import com.ctsi.procgroup.entity.CscpProcGroup;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.procgroup.entity.dto.QueryProcGroupDetailsDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
public interface CscpProcGroupMapper extends MybatisBaseMapper<CscpProcGroup> , SortMapper {

    /**
     * 查询权限组对应的用户
     *
     * @param procGroupIdList
     * @return
     */
    @InterceptorIgnore(others = "tenantId@true")
    List<QueryProcGroupDetailsDTO> details(@Param("procGroupId") List<Long> procGroupIdList);
}
