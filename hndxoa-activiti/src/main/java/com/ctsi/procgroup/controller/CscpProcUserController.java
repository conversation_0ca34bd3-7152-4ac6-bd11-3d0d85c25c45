package com.ctsi.procgroup.controller;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.procgroup.entity.CscpProcUser;
import com.ctsi.procgroup.entity.dto.CscpProcUserDTO;
import com.ctsi.procgroup.service.ICscpProcUserService;
import com.ctsi.ssdc.admin.domain.dto.CscpOtherListRealName;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpProcUser")
@Api(value = "给工作组添加用户", tags = "给工作组添加用户接口")
public class CscpProcUserController extends BaseController {

    private static final String ENTITY_NAME = "cscpProcUser";

    @Autowired
    private ICscpProcUserService cscpProcUserService;


    /**
     * 新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增工作组用户批量数据")
    public ResultVO createBatch(@RequestBody List<CscpProcUserDTO> cscpProcUserList) {
        Boolean result = cscpProcUserService.insertBatch(cscpProcUserList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增工作组用户数据")
    public ResultVO<CscpProcUserDTO> create(@RequestBody CscpProcUserDTO cscpProcUserDTO) throws URISyntaxException {
        CscpProcUserDTO result = cscpProcUserService.create(cscpProcUserDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新工作组用户数据")
    public ResultVO update(@RequestBody CscpProcUserDTO cscpProcUserDTO) {
        Assert.notNull(cscpProcUserDTO.getId(), "general.IdNotNull");
        int count = cscpProcUserService.update(cscpProcUserDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除工作组用户数据")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpProcUserService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        CscpProcUserDTO cscpProcUserDTO = cscpProcUserService.findOne(id);
        return ResultVO.success(cscpProcUserDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryCscpProcUserPage/{id}")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<CscpUserDTO>> queryCscpProcUserPage(@PathVariable Long id, BasePageForm basePageForm) {
        PageResult<CscpUserDTO> cscpUserDTOPageResult =  cscpProcUserService.queryUserListById(id, basePageForm);
        return ResultVO.success(cscpUserDTOPageResult);
    }



    /**
     * 分页查询多条数据.
     */
    @PostMapping("/queryCscpUserByCscpGroupListIds")
    @ApiOperation(value = "查询该流程工作组下面的所有人员", notes = "传入参数")
    public ResultVO<PageResult<CscpUserDTO>> queryCscpUserByCscpGroupListIds(@RequestBody CscpOtherListRealName cscpOtherListRealName,
                                                                        BasePageForm basePageForm) {
        PageResult<CscpUserDTO> cscpUserDTOPageResult =  cscpProcUserService.queryCscpUserByCscpGroupListIds(cscpOtherListRealName, basePageForm);
        return ResultVO.success(cscpUserDTOPageResult);
    }

}
