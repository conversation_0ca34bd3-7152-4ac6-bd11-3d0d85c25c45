package com.ctsi.procgroup.controller;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.procgroup.entity.CscpProcOrg;
import com.ctsi.procgroup.entity.dto.CscpProcOrgDTO;
import com.ctsi.procgroup.service.ICscpProcOrgService;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpProcOrg")
@Api(value = "给工作组添加角色", tags = "给工作组添加角色接口")
public class CscpProcOrgController extends BaseController {

    private static final String ENTITY_NAME = "cscpProcOrg";

    @Autowired
    private ICscpProcOrgService cscpProcOrgService;


    /**
     * 新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增工作组角色批量数据")
    public ResultVO createBatch(@RequestBody List<CscpProcOrgDTO> cscpProcOrgList) {
        Boolean result = cscpProcOrgService.insertBatch(cscpProcOrgList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新工作组角色数据")
    public ResultVO update(@RequestBody CscpProcOrgDTO cscpProcOrgDTO) {
        Assert.notNull(cscpProcOrgDTO.getId(), "general.IdNotNull");
        int count = cscpProcOrgService.update(cscpProcOrgDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除工作组角色数据")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpProcOrgService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        CscpProcOrgDTO cscpProcOrgDTO = cscpProcOrgService.findOne(id);
        return ResultVO.success(cscpProcOrgDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryCscpProcOrgPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<IPage<CscpProcOrgDTO>> queryCscpProcOrgPage(CscpProcOrgDTO cscpProcOrgDTO, BasePageForm basePageForm) {
        IPage<CscpProcOrgDTO> cscpProcOrgList = cscpProcOrgService.queryListPage(cscpProcOrgDTO, basePageForm);
        return ResultVO.success(cscpProcOrgList);
    }

}
