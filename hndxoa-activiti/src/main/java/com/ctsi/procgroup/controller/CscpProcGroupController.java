package com.ctsi.procgroup.controller;

import java.net.URISyntaxException;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.procgroup.entity.dto.CscpAddProcDTO;
import com.ctsi.procgroup.entity.dto.CscpProcGroupDTO;
import com.ctsi.procgroup.entity.dto.CscpProcUserDTO;
import com.ctsi.procgroup.entity.dto.QueryProcGroupDetailsDTO;
import com.ctsi.procgroup.service.ICscpProcGroupService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpProcGroup")
@Api(value = "工作组权限", tags = "工作组权限接口")
public class CscpProcGroupController extends BaseController {

    private static final String ENTITY_NAME = "cscpProcGroup";

    @Autowired
    private ICscpProcGroupService cscpProcGroupService;


    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增工作组权限数据")
    public ResultVO<CscpAddProcDTO> create(@RequestBody CscpAddProcDTO cscpProcGroupDTO) throws URISyntaxException {
        CscpAddProcDTO cscpAddProcDTO = cscpProcGroupService.create(cscpProcGroupDTO);
        return ResultVO.success(cscpAddProcDTO);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新工作组权限数据")
    public ResultVO update(@RequestBody CscpAddProcDTO cscpAddProcDTO) {
        int count = cscpProcGroupService.update(cscpAddProcDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除工作组权限数据")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpProcGroupService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        CscpAddProcDTO one = cscpProcGroupService.findOne(id);
        return ResultVO.success(one);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryCscpProcGroupPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<IPage<CscpProcGroupDTO>> queryCscpProcGroupPage(CscpProcGroupDTO cscpProcGroupDTO, BasePageForm basePageForm) {
        IPage<CscpProcGroupDTO> cscpProcGroupList = cscpProcGroupService.queryListPage(cscpProcGroupDTO, basePageForm);
        return ResultVO.success(cscpProcGroupList);
    }


    /**
     * 查询本单位授权的权限组
     *
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryProcGroupPage")
    @ApiOperation(value = "查询本单位授权的权限组", notes = "传入参数")
    public ResultVO<IPage<CscpProcGroupDTO>> queryProcGroupPage(BasePageForm basePageForm) {
        IPage<CscpProcGroupDTO> cscpProcGroupDTOIPage = cscpProcGroupService.queryPage(basePageForm);
        return ResultVO.success(cscpProcGroupDTOIPage);
    }


    /**
     * 查询授权给本单位的权限组
     *
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryProcGroupDetailsPage")
    @ApiOperation(value = "查询授权给本单位的权限组中的详细信息（用户信息和单位信息）", notes = "传入参数")
    public ResultVO<PageResult<QueryProcGroupDetailsDTO>> queryProcGroupDetailsPage(BasePageForm basePageForm) {
        return ResultVO.success(cscpProcGroupService.queryProcGroupDetailsPage(basePageForm));
    }

    /**
     * 根据权限组id查询对应的用户
     *
     * @param basePageForm
     * @returnZ
     */
    @GetMapping("/queryProcUserPage/{id}")
    @ApiOperation(value = "根据权限组id查询对应的用户", notes = "传入参数")
    public ResultVO<PageResult<CscpProcUserDTO>> queryProcUserPage(@PathVariable Long id, BasePageForm basePageForm) {
        PageResult<CscpProcUserDTO> procUserList = cscpProcGroupService.queryProcUserPage(id, basePageForm);
        return ResultVO.success(procUserList);
    }

}
