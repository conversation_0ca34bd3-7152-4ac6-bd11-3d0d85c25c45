package com.ctsi.procgroup.entity;

import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.common.ProcessBusinessBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CscpProcUser对象", description = "")
public class CscpProcUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 权限组id
     */
    @ApiModelProperty(value = "权限组id")
    @TableField("PERMISSION_GROUP_ID")
    private Long permissionGroupId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("USER_ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField("real_NAME")
    private String realName;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "用户名称")
    @TableField("company_id")
    private Long companyId;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField("SORT")
    private Integer sort;


}
