package com.ctsi.procgroup.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data

@ApiModel(value = "CscpProcUserDTO对象", description = "")
public class CscpProcUserDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 权限组id
     */
    @ApiModelProperty(value = "权限组id")
    private Long permissionGroupId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String realName;



    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;


    @ApiModelProperty(value = "创建时间不设置值")
    private LocalDateTime createTime;


}
