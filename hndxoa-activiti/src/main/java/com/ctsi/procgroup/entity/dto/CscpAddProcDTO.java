package com.ctsi.procgroup.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CscpAddProcDTO {
    private CscpProcGroupDTO cscpProcGroupDTO;
    private List<CscpProcOrgDTO> cscpProcOrgDTOList;
    private List<CscpProcUserDTO> cscpProcUserDTOList;
}
