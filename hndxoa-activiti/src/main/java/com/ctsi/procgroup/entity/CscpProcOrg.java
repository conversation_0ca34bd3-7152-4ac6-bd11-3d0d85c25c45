package com.ctsi.procgroup.entity;

import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.common.ProcessBusinessBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CscpProcOrg对象", description = "")
public class CscpProcOrg extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 权限组id
     */
    @ApiModelProperty(value = "权限组id")
    @TableField("PERMISSION_GROUP_ID")
    private Long permissionGroupId;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    @TableField("ORG_ID")
    private Long orgId;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @TableField("ORG_NAME")
    private String orgName;


}
