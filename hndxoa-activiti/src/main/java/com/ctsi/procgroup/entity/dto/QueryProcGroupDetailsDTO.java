package com.ctsi.procgroup.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname QueryProcGroupDetailsDTO
 * @Description
 * @Date 2021/12/30 10:31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "放回权限组详情信息", description = "")
public class QueryProcGroupDetailsDTO extends BaseDtoEntity {

    /**
     * 权限名称
     */
    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


}
