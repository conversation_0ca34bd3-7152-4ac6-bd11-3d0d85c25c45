package com.ctsi.procgroup.entity;

import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.common.ProcessBusinessBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CscpProcGroup对象", description="")
public class CscpProcGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 权限名称
     */
    @ApiModelProperty(value = "权限名称")
    @TableField("PERMISSION_NAME")
    private String permissionName;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField("SORT")
    private Integer sort;


}
