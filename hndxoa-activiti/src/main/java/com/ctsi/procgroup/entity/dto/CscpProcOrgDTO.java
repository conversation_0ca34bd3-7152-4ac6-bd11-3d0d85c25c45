package com.ctsi.procgroup.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CscpProcOrgDTO对象", description = "")
public class CscpProcOrgDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 权限组id
     */
    @ApiModelProperty(value = "权限组id")
    private Long permissionGroupId;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long orgId;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String orgName;


    @ApiModelProperty(value = "创建时间不设置值")
    private LocalDateTime createTime;


}
