package com.ctsi.procgroup.service;

import com.ctsi.procgroup.entity.dto.CscpProcOrgDTO;
import com.ctsi.procgroup.entity.CscpProcOrg;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
public interface ICscpProcOrgService extends SysBaseServiceI<CscpProcOrg> {


    /**
     * 分页查询
     */
    IPage<CscpProcOrgDTO> queryListPage(CscpProcOrgDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<CscpProcOrgDTO> queryList(Long id);

    /**
     * 根据主键id获取单个对象
     */
    CscpProcOrgDTO findOne(Long id);

    /**
     * 新增
     */
    CscpProcOrgDTO create(CscpProcOrgDTO entity);


    /**
     * 更新
     */
    int update(CscpProcOrgDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

    /**
     * 是否存在
     * existByCscpProcOrgId
     */
    boolean existByCscpProcOrgId(Long code);

    /**
     * 批量新增
     * create batch
     */
    Boolean insertBatch(List<CscpProcOrgDTO> dataList);


}
