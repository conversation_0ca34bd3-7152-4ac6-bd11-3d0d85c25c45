package com.ctsi.procgroup.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.procgroup.entity.CscpProcGroup;
import com.ctsi.procgroup.entity.CscpProcOrg;
import com.ctsi.procgroup.entity.CscpProcUser;
import com.ctsi.procgroup.entity.dto.*;
import com.ctsi.procgroup.mapper.CscpProcGroupMapper;
import com.ctsi.procgroup.mapper.CscpProcOrgMapper;
import com.ctsi.procgroup.mapper.CscpProcUserMapper;
import com.ctsi.procgroup.service.ICscpProcGroupService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

import javax.enterprise.inject.spi.Bean;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */

@Slf4j
@Service
public class CscpProcGroupServiceImpl extends SysBaseServiceImpl<CscpProcGroupMapper, CscpProcGroup> implements ICscpProcGroupService {

    @Autowired
    private CscpProcGroupMapper cscpProcGroupMapper;
    @Autowired
    private CscpProcOrgServiceImpl cscpProcOrgService;
    @Autowired
    private CscpProcUserServiceImpl cscpProcUserService;
    @Autowired
    private CscpProcOrgMapper cscpProcOrgMapper;
    @Autowired
    private CscpProcUserMapper cscpProcUserMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public IPage<CscpProcGroupDTO> queryListPage(CscpProcGroupDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<CscpProcGroup> queryWrapper = new LambdaQueryWrapper();

        queryWrapper.like(
                null != entityDTO.getPermissionName() && !"".equals(entityDTO.getPermissionName()),
                CscpProcGroup::getPermissionName,
                entityDTO.getPermissionName());
        queryWrapper.orderByAsc(CscpProcGroup::getSort);

        IPage<CscpProcGroup> pageData = cscpProcGroupMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<CscpProcGroupDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, CscpProcGroupDTO.class));
        return data;
    }


    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public CscpAddProcDTO findOne(Long id) {
        CscpAddProcDTO cscpAddProcDTO = CscpAddProcDTO.builder()
                .cscpProcGroupDTO(BeanConvertUtils.copyProperties(cscpProcGroupMapper.selectById(id), CscpProcGroupDTO.class))
                .cscpProcOrgDTOList(cscpProcOrgService.queryList(id).stream().map(i -> BeanConvertUtils.copyProperties(i, CscpProcOrgDTO.class)).collect(Collectors.toList()))
                .cscpProcUserDTOList(cscpProcUserService.queryList(id).stream().map(i -> BeanConvertUtils.copyProperties(i, CscpProcUserDTO.class)).collect(Collectors.toList()))
                .build();
        return cscpAddProcDTO;
    }


    /**
     * 新增
     *
     * @param cscpProcGroupDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public CscpAddProcDTO create(CscpAddProcDTO cscpProcGroupDTO) {
        this.updateSort(cscpProcGroupDTO);
        CscpProcGroup cscpProcGroup = BeanConvertUtils.copyProperties(cscpProcGroupDTO.getCscpProcGroupDTO(), CscpProcGroup.class);
        //新增工作组权限
        save(cscpProcGroup);

        List<CscpProcOrg> cscpProcOrgs = cscpProcGroupDTO.getCscpProcOrgDTOList()
                .stream()
                .map(i -> {
                    i.setPermissionGroupId(cscpProcGroup.getId());
                    return BeanConvertUtils.copyProperties(i, CscpProcOrg.class);
                })
                .collect(Collectors.toList());

        List<CscpProcUser> cscpProcUsers = cscpProcGroupDTO.getCscpProcUserDTOList()
                .stream()
                .map(i -> {
                    i.setPermissionGroupId(cscpProcGroup.getId());
                    return BeanConvertUtils.copyProperties(i, CscpProcUser.class);
                })
                .collect(Collectors.toList());

        //新增单位
        cscpProcOrgService.saveBatch(cscpProcOrgs);
        //新增用户
        cscpProcUserService.saveBatch(cscpProcUsers);

        return cscpProcGroupDTO;
    }

    /**
     * 修改
     *
     * @param cscpAddProcDTO the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(CscpAddProcDTO cscpAddProcDTO) {
        try {
            //修改工作组权限
            this.updateSort(cscpAddProcDTO);
            cscpProcGroupMapper.updateById(BeanConvertUtils.copyProperties(cscpAddProcDTO.getCscpProcGroupDTO(), CscpProcGroup.class));
            //删除工作组对呀的用户和单位
            LambdaQueryWrapper<CscpProcOrg> cscpProcOrgLambdaQueryWrapper = new LambdaQueryWrapper();
            cscpProcOrgLambdaQueryWrapper.eq(CscpProcOrg::getPermissionGroupId, cscpAddProcDTO.getCscpProcGroupDTO().getId());
            cscpProcOrgMapper.delete(cscpProcOrgLambdaQueryWrapper);
            LambdaQueryWrapper<CscpProcUser> cscpProcUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cscpProcUserLambdaQueryWrapper.eq(CscpProcUser::getPermissionGroupId, cscpAddProcDTO.getCscpProcGroupDTO().getId());
            cscpProcUserMapper.delete(cscpProcUserLambdaQueryWrapper);


            List<CscpProcOrg> cscpProcOrgs = cscpAddProcDTO.getCscpProcOrgDTOList()
                    .stream()
                    .map(i -> {
                        i.setId(null);
                        return BeanConvertUtils.copyProperties(i, CscpProcOrg.class);
                    })
                    .collect(Collectors.toList());

            List<CscpProcUser> cscpProcUsers = cscpAddProcDTO.getCscpProcUserDTOList()
                    .stream()
                    .map(i -> {
                        i.setId(null);
                        return BeanConvertUtils.copyProperties(i, CscpProcUser.class);
                    })
                    .collect(Collectors.toList());
            //新增单位
            cscpProcOrgService.saveBatch(cscpProcOrgs);
            //新增用户
            cscpProcUserService.saveBatch(cscpProcUsers);
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        try {
            //删除工作组权限对应单位
            LambdaQueryWrapper<CscpProcOrg> cscpProcOrgLambdaQueryWrapper = new LambdaQueryWrapper();
            cscpProcOrgLambdaQueryWrapper.eq(CscpProcOrg::getPermissionGroupId, id);
            cscpProcOrgMapper.delete(cscpProcOrgLambdaQueryWrapper);
            //删除工作组权限对应的用户
            LambdaQueryWrapper<CscpProcUser> cscpProcUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cscpProcUserLambdaQueryWrapper.eq(CscpProcUser::getPermissionGroupId, id);
            cscpProcUserMapper.delete(cscpProcUserLambdaQueryWrapper);
            //删除工作组权限
            cscpProcGroupMapper.deleteById(id);
            return 1;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public IPage<CscpProcGroupDTO> queryPage(BasePageForm basePageForm) {
        LambdaQueryWrapper<CscpProcOrg> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcOrg::getOrgId, SecurityUtils.getCurrentUser().get().getCompanyId());
        List<Long> collect = cscpProcOrgMapper.selectListNoAdd(lambdaQueryWrapper).stream().map(i -> i.getPermissionGroupId()).collect(Collectors.toList());

        if (!collect.isEmpty()) {
            LambdaQueryWrapper<CscpProcGroup> cpglq = new LambdaQueryWrapper<>();
            cpglq.in(CscpProcGroup::getId, collect);
            IPage<CscpProcGroup> pageData = cscpProcGroupMapper.selectPageNoAdd(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm), cpglq);
            //返回
            return pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, CscpProcGroupDTO.class));
        }
        return null;
    }

    /**
     * 查询授权给本单位的权限组中的详细信息（用户信息和单位信息）
     *
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<QueryProcGroupDetailsDTO> queryProcGroupDetailsPage(BasePageForm basePageForm) {
        //查询授权给本单位的权限组
        List<CscpProcOrg> cscpProcOrgs = cscpProcOrgMapper.selectListOnlyAddTenantId(
                new LambdaQueryWrapper<CscpProcOrg>().eq(CscpProcOrg::getOrgId, SecurityUtils.getCurrentCscpUserDetail().getCompanyId()));
        List<Long> procOrgList = cscpProcOrgs.stream().map(i -> i.getPermissionGroupId()).collect(Collectors.toList());
        if (null == procOrgList || procOrgList.size() == 0) {
            return new PageResult<QueryProcGroupDetailsDTO>(new ArrayList<QueryProcGroupDetailsDTO>(),
                    0, 0);
        }
        IPage cscpProcGroupPage = cscpProcGroupMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), new LambdaQueryWrapper<CscpProcGroup>().in(CscpProcGroup::getId, procOrgList));
        List<QueryProcGroupDetailsDTO> queryProcGroupDetailsList = ListCopyUtil.copy(cscpProcGroupPage.getRecords(), QueryProcGroupDetailsDTO.class);
        return new PageResult<QueryProcGroupDetailsDTO>(queryProcGroupDetailsList,
                cscpProcGroupPage.getTotal(), cscpProcGroupPage.getCurrent());
    }

    /**
     * 根据权限组id查询对应的用户
     *
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<CscpProcUserDTO> queryProcUserPage(Long id, BasePageForm basePageForm) {
        //根据权限组id查询对应的用户
        IPage<CscpProcUser> pageData = cscpProcUserMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                new LambdaQueryWrapper<CscpProcUser>().eq(CscpProcUser::getPermissionGroupId, id)
        );
        IPage<CscpProcUserDTO> convert = pageData.convert(i -> BeanConvertUtils.copyProperties(i, CscpProcUserDTO.class));

        return new PageResult<CscpProcUserDTO>(convert.getRecords(),
                pageData.getTotal(), pageData.getCurrent());
    }

    /**
     * 排序号自增
     * @param cscpAddProcDTO
     * @return
     */
    @Override
    public Boolean updateSort(CscpAddProcDTO cscpAddProcDTO){
        LambdaQueryWrapper<CscpProcGroup> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(CscpProcGroup::getTenantId,SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        lambdaQueryWrapper.eq(CscpProcGroup::getSort,cscpAddProcDTO.getCscpProcGroupDTO().getSort());
        int count = cscpProcGroupMapper.selectCount(lambdaQueryWrapper);
        if (count > 0){
            cscpProcGroupMapper.updataSort(
                    SortEnum.builder()
                            .sort(cscpAddProcDTO.getCscpProcGroupDTO().getSort())
                            .id(cscpAddProcDTO.getCscpProcGroupDTO().getId())
                            .tenantId(SecurityUtils.getCurrentCscpUserDetail().getTenantId())
                            .tableName("cscp_proc_group")
                            .sortName("sort")
                            .additionOrsubtraction("+")
                            .build());
        }
        return true;
    }
}
