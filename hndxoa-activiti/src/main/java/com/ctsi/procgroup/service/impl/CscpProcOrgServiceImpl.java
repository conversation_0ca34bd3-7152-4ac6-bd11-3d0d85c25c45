package com.ctsi.procgroup.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.procgroup.entity.CscpProcOrg;
import com.ctsi.procgroup.entity.dto.CscpProcOrgDTO;
import com.ctsi.procgroup.mapper.CscpProcOrgMapper;
import com.ctsi.procgroup.service.ICscpProcOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */

@Slf4j
@Service
public class CscpProcOrgServiceImpl extends SysBaseServiceImpl<CscpProcOrgMapper, CscpProcOrg> implements ICscpProcOrgService {

    @Autowired
    private CscpProcOrgMapper cscpProcOrgMapper;

    /**
     * 翻页
     *
     * @param entity
     * @param pageable
     * @return
     */
    @Override
    public IPage<CscpProcOrgDTO> queryListPage(CscpProcOrgDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<CscpProcOrg> queryWrapper = new LambdaQueryWrapper();

        IPage<CscpProcOrg> pageData = cscpProcOrgMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<CscpProcOrgDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, CscpProcOrgDTO.class));
        return data;
    }

    /**
     * 列表查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<CscpProcOrgDTO> queryList(Long id) {
        LambdaQueryWrapper<CscpProcOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpProcOrg::getPermissionGroupId, id);
        List<CscpProcOrg> listData = cscpProcOrgMapper.selectList(queryWrapper);
        List<CscpProcOrgDTO> CscpProcOrgDTOList = ListCopyUtil.copy(listData, CscpProcOrgDTO.class);
        return CscpProcOrgDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public CscpProcOrgDTO findOne(Long id) {
        CscpProcOrg cscpProcOrg = cscpProcOrgMapper.selectById(id);
        return BeanConvertUtils.copyProperties(cscpProcOrg, CscpProcOrgDTO.class);
    }


    /**
     * 新增
     *
     * @param entity the entity to create
     * @return
     */
    @Override
    @Transactional
    public CscpProcOrgDTO create(CscpProcOrgDTO entityDTO) {
        CscpProcOrg cscpProcOrg = BeanConvertUtils.copyProperties(entityDTO, CscpProcOrg.class);
        save(cscpProcOrg);
        return BeanConvertUtils.copyProperties(cscpProcOrg, CscpProcOrgDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(CscpProcOrgDTO entity) {
        CscpProcOrg cscpProcOrg = BeanConvertUtils.copyProperties(entity, CscpProcOrg.class);
        return cscpProcOrgMapper.updateById(cscpProcOrg);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return cscpProcOrgMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param CscpProcOrgId
     * @return
     */
    @Override
    public boolean existByCscpProcOrgId(Long CscpProcOrgId) {
        if (CscpProcOrgId != null) {
            LambdaQueryWrapper<CscpProcOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpProcOrg::getId, CscpProcOrgId);
            List<CscpProcOrg> result = cscpProcOrgMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional
    public Boolean insertBatch(List<CscpProcOrgDTO> dataList) {
        List<CscpProcOrg> result = ListCopyUtil.copy(dataList, CscpProcOrg.class);
        return saveBatch(result);
    }


}
