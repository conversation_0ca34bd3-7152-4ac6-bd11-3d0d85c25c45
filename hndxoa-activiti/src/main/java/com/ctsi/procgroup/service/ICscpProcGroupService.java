package com.ctsi.procgroup.service;

import com.ctsi.procgroup.entity.dto.CscpAddProcDTO;
import com.ctsi.procgroup.entity.dto.CscpProcGroupDTO;
import com.ctsi.procgroup.entity.CscpProcGroup;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.procgroup.entity.dto.CscpProcUserDTO;
import com.ctsi.procgroup.entity.dto.QueryProcGroupDetailsDTO;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
public interface ICscpProcGroupService extends SysBaseServiceI<CscpProcGroup> {


    /**
     * 分页查询
     */
    IPage<CscpProcGroupDTO> queryListPage(CscpProcGroupDTO entityDTO, BasePageForm page);

    /**
     * 根据主键id获取单个对象
     */
    CscpAddProcDTO findOne(Long id);

    /**
     * 新增
     */
    CscpAddProcDTO create(CscpAddProcDTO cscpProcGroupDTO);


    /**
     * 更新
     */
    int update(CscpAddProcDTO cscpAddProcDTO);

    /**
     * 删除
     */
    int delete(Long id);

    /**
     * @param basePageForm
     * @return
     */
    IPage<CscpProcGroupDTO> queryPage(BasePageForm basePageForm);

    /**
     * 查询授权给本单位的权限组中的详细信息（用户信息和单位信息）
     *
     * @param basePageForm
     * @return
     */
    PageResult<QueryProcGroupDetailsDTO> queryProcGroupDetailsPage(BasePageForm basePageForm);


    /**
     * 根据权限组id查询对应的用户
     *
     * @param basePageForm
     * @return
     */
    PageResult<CscpProcUserDTO> queryProcUserPage(Long id,BasePageForm basePageForm);

    /**
     * 排序号自增
     * @param cscpAddProcDTO
     * @return
     */
    Boolean updateSort(CscpAddProcDTO cscpAddProcDTO);
}
