package com.ctsi.procgroup.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.UserConstant;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.procgroup.entity.CscpProcUser;
import com.ctsi.procgroup.entity.dto.CscpProcUserDTO;
import com.ctsi.procgroup.mapper.CscpProcUserMapper;
import com.ctsi.procgroup.service.ICscpProcUserService;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.CscpOtherListRealName;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */

@Slf4j
@Service
public class CscpProcUserServiceImpl extends SysBaseServiceImpl<CscpProcUserMapper, CscpProcUser> implements ICscpProcUserService {

    @Autowired
    private CscpProcUserMapper cscpProcUserMapper;

    @Autowired
    private CscpUserService  cscpUserService;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    /**
     * 翻页
     *
     * @param id
     * @param basePageForm
     * @return
     */
    @Override
    public IPage<CscpProcUserDTO> queryListPage(Long id, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<CscpProcUser> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpProcUser::getPermissionGroupId, id);
        IPage<CscpProcUser> pageData = cscpProcUserMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<CscpProcUserDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, CscpProcUserDTO.class));
        return data;
    }

    /**
     * 列表查询
     *
     * @param id
     * @return
     */
    @Override
    public List<CscpProcUserDTO> queryList(Long id) {
        LambdaQueryWrapper<CscpProcUser> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpProcUser::getPermissionGroupId, id);
        List<CscpProcUser> listData = cscpProcUserMapper.selectList(queryWrapper);
        List<CscpProcUserDTO> CscpProcUserDTOList = ListCopyUtil.copy(listData, CscpProcUserDTO.class);
        return CscpProcUserDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public CscpProcUserDTO findOne(Long id) {
        CscpProcUser cscpProcUser = cscpProcUserMapper.selectById(id);
        return BeanConvertUtils.copyProperties(cscpProcUser, CscpProcUserDTO.class);
    }


    /**
     * 新增
     *
     * @param entity the entity to create
     * @return
     */
    @Override
    @Transactional
    public CscpProcUserDTO create(CscpProcUserDTO entityDTO) {
        CscpProcUser cscpProcUser = BeanConvertUtils.copyProperties(entityDTO, CscpProcUser.class);
        save(cscpProcUser);
        return BeanConvertUtils.copyProperties(cscpProcUser, CscpProcUserDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(CscpProcUserDTO entity) {
        CscpProcUser cscpProcUser = BeanConvertUtils.copyProperties(entity, CscpProcUser.class);
        return cscpProcUserMapper.updateById(cscpProcUser);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return cscpProcUserMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param CscpProcUserId
     * @return
     */
    @Override
    public boolean existByCscpProcUserId(Long CscpProcUserId) {
        if (CscpProcUserId != null) {
            LambdaQueryWrapper<CscpProcUser> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpProcUser::getId, CscpProcUserId);
            List<CscpProcUser> result = cscpProcUserMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional
    public Boolean insertBatch(List<CscpProcUserDTO> dataList) {
        List<CscpProcUser> result = ListCopyUtil.copy(dataList, CscpProcUser.class);
        return saveBatch(result);
    }

    @Override
    public PageResult<CscpUserDTO> queryUserListById(Long id, BasePageForm basePageForm) {
        LambdaQueryWrapper<CscpProcUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpProcUser::getPermissionGroupId, id);
        List<Long> userIdList = cscpProcUserMapper.selectListOnlyAddTenantId(queryWrapper).stream()
                .map(x -> x.getUserId()).distinct().collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(userIdList)) {
            LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpUser::getStatus, UserConstant.USER_ACTIVE_STATUS);
            lambdaQueryWrapper.in(CscpUser::getId, userIdList).select(CscpUser::getId, CscpUser::getRealName);
            IPage<CscpUser> userIPage = cscpUserService.page(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm), lambdaQueryWrapper);

            List<CscpUserDTO> data = ListCopyUtil.copy(userIPage.getRecords(), CscpUserDTO.class);
            return new PageResult<CscpUserDTO>(data, userIPage.getTotal(), userIPage.getTotal());
        }
        return null;
    }

    @Override
    public PageResult<CscpUserDTO> queryCscpUserByCscpGroupListIds(CscpOtherListRealName cscpOtherListRealName, BasePageForm basePageForm) {
        LambdaQueryWrapper<CscpProcUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CscpProcUser::getPermissionGroupId, cscpOtherListRealName.getIds());
        List<Long> userIdList = cscpProcUserMapper.selectList(queryWrapper).stream()
                .map(x -> x.getUserId()).distinct().collect(Collectors.toList());

        if (userIdList.size() > 0) {
            LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(CscpUser::getId, userIdList);
            lambdaQueryWrapper.eq(CscpUser::getStatus, UserConstant.USER_ACTIVE_STATUS);
            if (StringUtils.isNotEmpty(cscpOtherListRealName.getRealName())) {
                String realName = cscpOtherListRealName.getRealName();
//                lambdaQueryWrapper.and(i -> i.eq(CscpUser::getRealName, KeyCenterUtils.encrypt(realName))
//                        .or().eq(CscpUser::getRealNameStart, KeyCenterUtils.encrypt(realName))
//                        .or().eq(CscpUser::getRealNameEnd, KeyCenterUtils.encrypt(realName))
//                );
                lambdaQueryWrapper.like(CscpUser::getRealNameStart, westoneEncryptService.isCipherMachine() ? westoneEncryptService.divisionEncryptRealNameWithFPE(realName) : this.division(realName));
            }
            lambdaQueryWrapper.orderByAsc(CscpUser::getOrderBy);
            IPage<CscpUser> userIPage = cscpUserService.page(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm), lambdaQueryWrapper);

            List<CscpUserDTO> data = ListCopyUtil.copy(userIPage.getRecords(), CscpUserDTO.class);
            return new PageResult<CscpUserDTO>(data, userIPage.getTotal(), userIPage.getTotal());
        }

        throw new BusinessException(ResultCode.RESULE_DATA_NONE);
    }

    //分割字符串加密
    public String division(String realName){
        String realNames = "";
        if (StringUtils.isNotBlank(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                realNames = realNames.concat(KeyCenterUtils.encrypt(realName.substring(i, i + 1)));
                realNames = realNames.concat(",");
            }
            realNames = realNames.substring(0, realNames.length() - 1);
        }
        return realNames;
    }
}
