package com.ctsi.procgroup.service;

import com.ctsi.procgroup.entity.dto.CscpProcUserDTO;
import com.ctsi.procgroup.entity.CscpProcUser;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.admin.domain.dto.CscpOtherListRealName;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
public interface ICscpProcUserService extends SysBaseServiceI<CscpProcUser> {


    /**
     * 分页查询
     */
    IPage<CscpProcUserDTO> queryListPage(Long id, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<CscpProcUserDTO> queryList(Long id);

    /**
     * 根据主键id获取单个对象
     */
    CscpProcUserDTO findOne(Long id);

    /**
     * 新增
     */
    CscpProcUserDTO create(CscpProcUserDTO entity);


    /**
     * 更新
     */
    int update(CscpProcUserDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

    /**
     * 是否存在
     * existByCscpProcUserId
     */
    boolean existByCscpProcUserId(Long code);

    /**
     * 批量新增
     * create batch
     */
    Boolean insertBatch(List<CscpProcUserDTO> dataList);

    /**
     * 根据组id查询下面的用户
     * @param id
     * @param basePageForm
     * @return
     */

    PageResult<CscpUserDTO> queryUserListById(Long id, BasePageForm basePageForm);

    PageResult<CscpUserDTO> queryCscpUserByCscpGroupListIds(CscpOtherListRealName cscpOtherListRealName, BasePageForm basePageForm);
}
