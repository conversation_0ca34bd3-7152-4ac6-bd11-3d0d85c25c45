package com.ctsi.interfaces.web;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.bpmn.dto.ProcessDataDTO;
import com.ctsi.activiti.bpmn.entity.CscpProc;
import com.ctsi.activiti.bpmn.service.ActivitiBpmnDefinitionService;
import com.ctsi.activiti.core.model.CompleteParam;
import com.ctsi.activiti.core.model.ExecutionLine;
import com.ctsi.activiti.core.model.StartParam;
import com.ctsi.activiti.core.service.ActivitiCoreService;
import com.ctsi.activiti.core.service.ActivitiQueryService;
import com.ctsi.activiti.core.vo.DetailTaskData;
import com.ctsi.activiti.core.vo.ProcessData;
import com.ctsi.activiti.core.vo.TaskData;
import com.ctsi.activiti.enums.SqlOperatorEnum;
import com.ctsi.aop.InterceptRequest;
import com.ctsi.business.domain.*;
import com.ctsi.business.repository.CscpProcTypeRepository;
import com.ctsi.business.service.*;
import com.ctsi.common.bo.PageActiviti;
import com.ctsi.common.bo.PageForm;
import com.ctsi.common.bo.Query;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.common.exception.CustomException;
import com.ctsi.common.utils.DateTimeUtil;
import com.ctsi.common.utils.PageData;
import com.ctsi.common.utils.SysErrEnum;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.interfaces.domain.InterfacesParams;
import com.ctsi.interfaces.service.CscpInterfacesService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/interfaces")
public class ActivitiCscpInterfacesController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CscpInterfacesService cscpInterfacesService;

    @Autowired
    private CscpActivitiService cscpActivitiService;

    @Autowired
    private CscpProcTypeRepository cscpProcTypeRepository;

    @Autowired
    private ActivitiCoreService activitiCoreService;

    @Autowired
    private ActivitiQueryService activitiQueryService;



    @Autowired
    private CscpOutAuthorizationService cscpOutAuthorizationService;

    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    @Autowired
    private CscpAuditLogService cscpAuditLogService;
    @Autowired
    private CscpAuditContentService cscpAuditContentService;

    @Autowired
    private ActivitiBpmnDefinitionService activitiBpmnDefinitionService;
    @Autowired
    private CscpUserService cscpUserService;
    /**
     * 获取表单参数
     * @param params
     * @return
     */
    @InterceptRequest
    @ApiOperation("个人申请时需要获取表单参数")
    @PostMapping("/getFormBaseParams")
    public ResponseBO<CscpBaseParams> getFormBaseParams(@RequestBody @ApiParam(required = true) Map<String,Object> params) {
        try {
            CscpBaseParams cscpBaseParams=cscpActivitiService.getFormBaseParams(params);
            return new ResponseBO<>(cscpBaseParams, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    @ApiOperation(value = "获取表单附件")
    @InterceptRequest
    @GetMapping("/getFormFileList")
    public ResponseBO<List<Map<String,Object>>> getFormFileList(@RequestParam("formId") String formId,
                                                                @RequestParam("formDataId") String formDataId,
                                                                @RequestParam( value = "formItemId", required = false) String formItemId){
        try {
            return cscpInterfacesService.getFormFileList(formId,formDataId);
        } catch (Exception e) {
            return new ResponseBO<>(e);
        }
    }

    /**
     * 下载表单附件
     * @param id
     * @param response
     * @param request
     */
    @ApiOperation(value = "下载表单附件")
    @GetMapping("/downloadFormFiles")
    public void downloadFormFiles(String id, HttpServletResponse response, HttpServletRequest request){
        try {
            cscpInterfacesService.downloadFormFile(id, response, request);
        }catch (Exception ex){

        }
    }

    /**
     * 获取流程模型列表
     * @param form
     * @return
     */
    @ApiOperation("获取流程模型列表")
    @InterceptRequest
    @PostMapping("/getProcessModelListForPage")
    public ResponseBO<PageActiviti<CscpProc>> getProcessModelListForPage(HttpServletRequest request, @RequestBody @ApiParam(required = true) PageForm<CscpProc> form) {
        try {
            String appCode=request.getHeader("appCode");
            //Map<String,Object> params=new HashMap<>();
           // params.put("appCode",appCode);
           // if(form.getT()!=null && StringUtils.isNotEmpty(form.getT().getTypeId())){
               // params.put("typeId",form.getT().getTypeId());
          //  }
            LambdaQueryWrapper<CscpProcType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpProcType::getAppCode, appCode)
                    .eq(form.getT()!=null && StringUtils.isNotEmpty(form.getT().getTypeId()),
                            CscpProcType::getTypeId,form.getT().getTypeId());
            List<CscpProcType> typeList= cscpProcTypeRepository.selectList(lambdaQueryWrapper);
            List<Query> queryList=new ArrayList<>();
            for(CscpProcType cscpProcType : typeList){
                Query query=new Query();
                query.setKey("TYPE_ID");
                query.setValue(cscpProcType.getTypeId());
                query.setOperator(SqlOperatorEnum.EQUALS.getKey());
                queryList.add(query);
            }
            form.setFlag(SqlOperatorEnum.OR.getKey());
            form.setQuery(queryList);
            PageActiviti<CscpProc> modelListListForPage = cscpActivitiService.getModelListListForPage(form);
            return new ResponseBO(modelListListForPage, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("getProcessModelListForPage Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @ApiOperation(value = "获取用户待办任务列表")
    @PostMapping("/getTodoTaskList")
    public ResponseBO<PageActiviti<DetailTaskData>> getTodoTaskList(HttpServletRequest request, @RequestBody @ApiParam(required = true) InterfacesParams interfacesParams) {
        try {
            String appCode=request.getHeader("appCode");
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            interfacesParams.setUserId(userId);
            interfacesParams.setAppCode(appCode);
            String dateStrNow = DateTimeUtil.getDateTimeString();

            /*CscpOutAuthorizationExample example = new CscpOutAuthorizationExample();
            CscpOutAuthorizationExample.Criteria cri = example.createCriteria();
            cri.andAuthorizeduseridEqualTo(userId);
            String dateStrNow = DateTimeUtil.getDateTimeString();
            cri.andStarttimeLessThanOrEqualTo(dateStrNow);
            cri.andEndtimeGreaterThanOrEqualTo(dateStrNow);*/
            LambdaQueryWrapper<CscpOutAuthorization> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.gt(CscpOutAuthorization::getStarttime,dateStrNow).le(CscpOutAuthorization::getEndtime,dateStrNow);

            List<CscpOutAuthorization> listOutAuth  = cscpOutAuthorizationService.list(lambdaQueryWrapper);
            Set<String> setAssign = new HashSet<>(1+listOutAuth.size());
            setAssign.add(userId);
            Optional.ofNullable(listOutAuth).ifPresent(loa->loa.forEach(oa->setAssign.add(oa.getUserid())));

            PageResult<DetailTaskData> pageResult= cscpInterfacesService.getActivelyTaskList(interfacesParams,setAssign.stream().collect(Collectors.toList()));
            PageActiviti<DetailTaskData> pageActiviti =new PageActiviti<>();
            pageActiviti.setData(pageResult.getData());
            pageActiviti.setTotal(Integer.valueOf(pageResult.getRecordsTotal()+""));
            pageActiviti.setCurrentPage(interfacesParams.getPageNumber());
            pageActiviti.setPageSize(interfacesParams.getPageSize());
            return new ResponseBO<>(pageActiviti, SysErrEnum.SUCCESS);
        }catch (Exception e){
            return new ResponseBO<>(e);
        }

    }

    @ApiOperation(value = "获取用户已办任务列表")
    @InterceptRequest
    @PostMapping("/getFinishTaskList")
    public ResponseBO<PageActiviti<ProcessData>>  getFinishTaskList(HttpServletRequest request, @RequestBody @ApiParam(required = true) InterfacesParams interfacesParams) {
        try{
            String appCode=request.getHeader("appCode");
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            interfacesParams.setAppCode(appCode);
            interfacesParams.setUserId(userId);
            PageResult<ProcessData> pageResult= cscpInterfacesService.getFinishTaskList(interfacesParams);

            PageActiviti<ProcessData> pageActiviti =new PageActiviti<>();
            pageActiviti.setData(pageResult.getData());
            pageActiviti.setTotal(Integer.valueOf(pageResult.getRecordsTotal()+""));
            pageActiviti.setCurrentPage(Integer.valueOf(interfacesParams.getPageSize()));
            pageActiviti.setPageSize(interfacesParams.getPageSize());
            return new ResponseBO<>(pageActiviti, SysErrEnum.SUCCESS);
        }catch (Exception e){
            e.printStackTrace();
            return new ResponseBO<>(e);
        }
    }

    @ApiOperation(value = "获取流程下的代办任务列表")
    @GetMapping("/getTodoTaskListByProcess")
    public PageResult<DetailTaskData> getTodoTaskListByProcess(
            @ApiParam(required = true, value = "当前页码") @RequestParam int pageNumber,
            @ApiParam(required = true, value = "每页长度") @RequestParam int pageSize,
            @ApiParam(required = true, value = "流程实例id") @RequestParam String processInstanceId,
            @ApiParam(required = true, value = "流程模型key") @RequestParam String processDefinitionKey) {
        return cscpInterfacesService.getTodoTaskListByProcess(pageNumber, pageSize, processInstanceId, processDefinitionKey);
    }

    @ApiOperation(value = "获取流程下的已办任务列表")
    @GetMapping("/getFinishTaskListByProcess")
    public PageResult<ProcessData> getFinishTaskListByProcess(
            @ApiParam(required = true, value = "当前页码") @RequestParam int pageNumber,
            @ApiParam(required = true, value = "每页长度") @RequestParam int pageSize,
            @ApiParam(required = true, value = "流程实例id") @RequestParam String processInstanceId,
            @ApiParam(required = true, value = "流程模型key") @RequestParam String processDefinitionKey) {
        return cscpInterfacesService.getFinishTaskListByProcess(pageNumber, pageSize, processInstanceId, processDefinitionKey);
    }

    @ApiOperation(value = "获取流程状态")
    @GetMapping("/getProcessStatus/{bid}")
    public ResponseBO<Map<String,Object>> getProcessStatus(@ApiParam(required = true, value = "业务ID") @PathVariable String bid) {
        try {
            CscpProcBase cscpProcBase=cscpProcBaseService.getProcBaseByBid(bid);
            if(cscpProcBase==null){
                throw new CustomException("bid关联的业务数据不存在");
            }

            Map<String,Object> processInfo=cscpInterfacesService.getProcessStatus(cscpProcBase);
            return new ResponseBO<>(processInfo, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("getProcessModelListForPage Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @InterceptRequest
    @GetMapping("/cscpOpinionsList")
    public ResponseBO<Void> getCscpOpinionsList() {
        log.debug("REST request to get CscpOpinionsList");
        String uid = SecurityUtils.getCurrentUserId() +"";
        /*CscpOpinionExample cscpOpinionExample = new CscpOpinionExample();
        cscpOpinionExample.createCriteria().andUserIdEqualTo(uid);*/
        return new ResponseBO<>(SysErrEnum.SUCCESS);
    }


    @GetMapping("/cscpTestData")
    public ResponseBO<List<Map<String,Object>>> cscpTestData() {
        List<Map<String,Object>> list=new ArrayList<>();

        List<Map<String,Object>> children1=new ArrayList<>();
        Map<String,Object> cmap1=new HashMap<>();
        cmap1.put("value","gugong");
        cmap1.put("label","故宫");
        children1.add(cmap1);

        Map<String,Object> cmap2=new HashMap<>();
        cmap2.put("value","tiantan");
        cmap2.put("label","天坛");
        children1.add(cmap2);

        Map<String,Object> cmap3=new HashMap<>();
        cmap3.put("value","wangfujing");
        cmap3.put("label","王府井");
        children1.add(cmap3);

        Map<String,Object> map1=new HashMap<>();
        map1.put("value","beijing");
        map1.put("label","北京");
        map1.put("children",children1);

        list.add(map1);

        List<Map<String,Object>> children2=new ArrayList<>();
        Map<String,Object> cmap11=new HashMap<>();
        cmap11.put("value","nanjing");
        cmap11.put("label","南京");
        List<Map<String,Object>> children22=new ArrayList<>();
        Map<String,Object> cmap22=new HashMap<>();
        cmap22.put("value","fuzimiao");
        cmap22.put("label","夫子庙");
        children22.add(cmap22);
        cmap11.put("children",children22);
        children2.add(cmap11);




        Map<String,Object> cmap221=new HashMap<>();
        cmap221.put("value","suzhou");
        cmap221.put("label","苏州");
        List<Map<String,Object>> children222=new ArrayList<>();
        Map<String,Object> cmap2221=new HashMap<>();
        cmap2221.put("value","zhuozhengyuan");
        cmap2221.put("label","拙政园");

        children222.add(cmap2221);

        Map<String,Object> cmap2222=new HashMap<>();
        cmap2222.put("value","shizilin");
        cmap2222.put("label","狮子林");

        children222.add(cmap2222);

        cmap221.put("children",children222);
        children2.add(cmap221);

        Map<String,Object> map2=new HashMap<>();
        map2.put("value","jiangsu");
        map2.put("label","江苏");
        map2.put("children",children2);

        list.add(map2);

        return new ResponseBO<>(list,SysErrEnum.SUCCESS);
    }



    /**
     * 启动流程
     *
     * @param requestBody
     * @return
     */
    @ApiOperation(value = "保存表单并初次启动流程")
    @InterceptRequest
    @PostMapping("/saveFormAndStartProcess")
    @OperationLog(dBOperation = DBOperation.ADD,message = "保存表单并初次启动流程")
    public ResponseBO<Map<String,Object>>  saveFormAndStartProcess(@ApiParam(required = true, value = "流程启动参数") @RequestBody Map<String, StartParam> requestBody,HttpServletRequest request) {
        try {
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }

            // 调用流程启动方法
            StartParam startParam = requestBody.get("data");
            if(StringUtils.isEmpty(startParam.getStarter())){
                startParam.setStarter(userId);
            }

            if(StringUtils.isEmpty(startParam.getBid())){//第三方应用bid关联业务
                throw new CustomException("bid不能为空");
            }

           /* CscpProcBaseExample cscpProcBaseExample=new CscpProcBaseExample();
            CscpProcBaseExample.Criteria criteria=cscpProcBaseExample.createCriteria();
            criteria.andBidEqualTo(startParam.getBid());*/
            LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpProcBase::getBid,startParam.getBid());

            List<CscpProcBase> list=cscpProcBaseService.list(lambdaQueryWrapper);
            if(list!=null && list.size()>0){
                throw new CustomException("bid重复");
            }
            // 调用部署方法
            Map<String,Object> returnMap = activitiCoreService.start(startParam);
            return new ResponseBO<>(returnMap,SysErrEnum.SUCCESS);
        }catch (Exception e){
            return new ResponseBO<>(e);
        }
    }

    @InterceptRequest
    @ApiOperation(value = "启动无表单工作流程")
    @PostMapping("/startProcessWithNoForm")
    @OperationLog(dBOperation = DBOperation.ADD,message = "启动无表单工作流程")
    public ResponseBO<Map<String,Object>>  startProcessWithNoForm(@ApiParam(required = true, value = "流程启动参数") @RequestBody Map<String, StartParam> requestBody,HttpServletRequest request) {
        try {
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            // 调用流程启动方法
            StartParam startParam = requestBody.get("data");
            if(StringUtils.isEmpty(startParam.getStarter())){
                startParam.setStarter(userId);
            }
            if(StringUtils.isEmpty(startParam.getBid())){//第三方应用bid关联业务
                throw new CustomException("bid不能为空");
            }

           /* CscpProcBaseExample cscpProcBaseExample=new CscpProcBaseExample();
            CscpProcBaseExample.Criteria criteria=cscpProcBaseExample.createCriteria();
            criteria.andBidEqualTo(startParam.getBid());*/

            LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpProcBase::getBid,startParam.getBid());

            List<CscpProcBase> list=cscpProcBaseService.list(lambdaQueryWrapper);
            if(list!=null && list.size()>0){
                throw new CustomException("bid重复");
            }
            startParam.setNeedForm(false);

            // 调用部署方法
            Map<String,Object> returnMap = activitiCoreService.start(startParam);
            return new ResponseBO<>(returnMap,SysErrEnum.SUCCESS);
        }catch (Exception e){
            return new ResponseBO<>(e);
        }
    }


    /**
     * 提交流程任务
     */
    @InterceptRequest
    @ApiOperation(value = "提交流程任务")
    @PostMapping("/complete")
    @OperationLog(dBOperation = DBOperation.ADD,message = "提交流程任务")
    public ResponseBO<Boolean> complete(@ApiParam(required = true, value = "任务提交参数") @RequestBody Map<String, CompleteParam> requestBody,HttpServletRequest request) {
        try{
            CompleteParam completeParam = requestBody.get("data");
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            completeParam.setClaimer(userId);
            completeParam.setNeedForm(false);
            boolean result=activitiCoreService.complete(completeParam);
            return new ResponseBO<>(result,SysErrEnum.SUCCESS);
        }catch (Exception e){
            return new ResponseBO<>(e);
        }
    }

    @InterceptRequest
    @ApiOperation(value = "流程终止")
    @PostMapping("/end/{processInstanceId}")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "流程终止")
    public ResponseBO<Void> end(HttpServletRequest request,@ApiParam(required = true, value = "流程实例ID") @PathVariable String processInstanceId) {
       try {
           String userId=request.getHeader("userId");
           if(StringUtils.isEmpty(userId)) {
               throw new CustomException("header中缺少userId参数,当前登陆人");
           }
           activitiCoreService.end(processInstanceId, userId);
           return new ResponseBO<>(SysErrEnum.SUCCESS);
       }catch (Exception e){
           return new ResponseBO<>(e);
       }
    }

    @InterceptRequest
    @ApiOperation(value = "任务退回上一环节")
    @PostMapping("/backUp")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "任务退回上一环节")
    public ResponseBO<Void> backUp(@ApiParam(required = true, value = "流程实例ID") String PROCESS_INSTANCE_ID,
                                   @ApiParam(required = true, value = "任务ID") String TASK_ID,
                                   HttpServletRequest request, @RequestBody PageData pd) {
        try{
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            return activitiCoreService.backUp(pd);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "任务退回拟稿")
    @PostMapping("/backStart")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "任务退回拟稿")
    public ResponseBO<Void> backStart(@ApiParam(required = true, value = "流程实例ID") String PROCESS_INSTANCE_ID,
                                      @ApiParam(required = true, value = "任务ID") String TASK_ID,
                                      HttpServletRequest request, @RequestBody PageData pd) {
        try{
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            return activitiCoreService.backStart(pd);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "收回任务")
    @PostMapping("/recycle")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "收回任务")
    public ResponseBO<Void> recycle(@ApiParam(required = true, value = "流程实例ID") String PROCESS_INSTANCE_ID,
                                    @ApiParam(required = true, value = "任务ID") String taskId,
                                    HttpServletRequest request, @RequestBody PageData pd) {

        try {
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            return activitiCoreService.recycle(pd);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "作废任务")
    @PostMapping("/cancellation")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "作废任务")
    public ResponseBO<Void> cancellation(@ApiParam(required = true, value = "流程实例ID") String PROCESS_INSTANCE_ID,
                                         HttpServletRequest request, @RequestBody PageData pd) {

        try {
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            return activitiCoreService.cancellation(PROCESS_INSTANCE_ID);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "传阅")
    @PostMapping("/viewTask")
    @OperationLog(dBOperation = DBOperation.ADD,message = "传阅")
    public ResponseBO<Void> viewTask(@ApiParam(required = true, value = "流程实例ID") String PROCESS_INSTANCE_ID,
                                     @ApiParam(required = true, value = "流程定义ID") String PROCESS_DEFINITION_ID,
                                     @ApiParam(required = true, value = "传阅意见") String OPINION,
                                     @ApiParam(required = true, value = "被传阅人ID") String userIds,
                                     HttpServletRequest request, @RequestBody PageData pd) {

        try {
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            return activitiCoreService.viewTask(pd);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "已阅意见")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "已阅意见")
    @PostMapping("/viewTaskOpinion")
    public ResponseBO<Void> viewTaskOpinion(@ApiParam(required = true, value = "ID") String VIEW_ID,
                                            @ApiParam(required = true, value = "传阅意见") String OPINION,
                                            HttpServletRequest request, @RequestBody PageData pd) {

        try {
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            return activitiCoreService.viewTaskOpinion(pd);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "设置已阅")
    @PostMapping("/setViewTaskRead")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "设置已阅")
    public ResponseBO<Void> setViewTaskRead(@ApiParam(required = true, value = "ID") String VIEW_ID,
                                            HttpServletRequest request, @RequestBody PageData pd) {

        try {
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            activitiCoreService.setViewTaskRead(pd);

            return new ResponseBO<>(SysErrEnum.SUCCESS);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "任务调度")
    @PostMapping("/schedule")
    @OperationLog(dBOperation = DBOperation.ADD,message = "任务调度")
    public ResponseBO<Void> schedule(@ApiParam(required = true, value = "流程实例ID") String PROCESS_INSTANCE_ID,
                                     @ApiParam(required = true, value = "任务ID") String TASK_ID,
                                     @ApiParam(required = true, value = "用户ID") String TARGET_NODE_ASSIGNEES,
                                     @ApiParam(required = true, value = "目标环节") String TARGET_NODE,
                                     HttpServletRequest request, @RequestBody PageData pd) {
        try{
            String userId=request.getHeader("userId");
            if(StringUtils.isEmpty(userId)) {
                throw new CustomException("header中缺少userId参数,当前登陆人");
            }
            pd.put("currentUserId", userId);

            return activitiCoreService.schedule(pd);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "任务催办")
    @GetMapping("/urgeTask/{bid}")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "任务催办")
    public ResponseBO<Void> urgeTask( @ApiParam(required = true, value = "业务ID") @PathVariable String bid) {
        try{
            CscpProcBase cscpProcBase=cscpProcBaseService.getProcBaseByBid(bid);
            if(cscpProcBase==null){
                throw new CustomException("bid关联的业务数据不存在");
            }
            activitiCoreService.urgeTask(cscpProcBase.getProcInstId());
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }
    @InterceptRequest
    @ApiOperation(value = "任务移交")
    @GetMapping("/transferTask/{taskId}/{userId}")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "任务移交")
    public ResponseBO<Void> transferTask( @ApiParam(required = true, value = "当前任务ID") @PathVariable String taskId,
                                          @ApiParam(required = true, value = "移交用户ID") @PathVariable String userId) {
        try{
            activitiCoreService.transferTask(taskId,userId);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        }catch (Exception ex){
            ex.printStackTrace();
            return new ResponseBO<>(ex);
        }
    }

    /**
     * 获取候选人列表
     * @param taskId
     * @param nodeKey
     * @return
     */
    @InterceptRequest
    @GetMapping("/getNodeAssigneesList/{taskId}/{nodeKey}")
    public ResponseBO<List<CscpUserDTO>> getNodeAssigneesList(@PathVariable String taskId, @PathVariable String nodeKey) {
        try {
            List<CscpUserDTO> list=cscpActivitiService.getNodeAssigneesList(taskId,nodeKey);
            List<CscpUserDTO> listDetail = new ArrayList<>();
            for(CscpUserDTO user : list){
                if(user==null || user.getId()==null){
                    continue;
                }
                CscpUserDTO detailDto = cscpUserService.findByUserId(user.getId());
                if(detailDto==null){
                    continue;
                }
                detailDto.setLoginName(user.getLoginName());
                listDetail.add(detailDto);
            }

            return new ResponseBO<>(listDetail, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("getNodeAssigneesList Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @InterceptRequest
    @ApiOperation(value = "获取任务可用的出口路线")
    @GetMapping("/getLineList/{taskId}")
    public ResponseBO<List<ExecutionLine>> getLineList(@ApiParam(required = true, value = "任务ID") @PathVariable String taskId) {
        try{
            List<ExecutionLine> list=activitiQueryService.getOutLineList(taskId);
            return new ResponseBO<>(list,SysErrEnum.SUCCESS);
        } catch (Exception e){
            return new ResponseBO<>(e);
        }
    }

    @InterceptRequest
    @ApiOperation(value = "根据流程实例ID，获取流程实例的流程运行图")
    @GetMapping("/getRouteNodes/{bid}")
    public ResponseBO<Map<String,Object>> getRouteNodes(@ApiParam(required = true, value = "业务ID") @PathVariable String bid) {

        try{
            CscpProcBase cscpProcBase=cscpProcBaseService.getProcBaseByBid(bid);
            if(cscpProcBase==null){
                throw new CustomException("bid关联的业务数据不存在");
            }
            List<TaskData> taskDataList= activitiQueryService.getProcessInstanceDiagram(cscpProcBase.getProcInstId());

            CscpProc cscpProc =activitiQueryService.getByProcessInstanceId(cscpProcBase.getProcInstId());
            Map<String,Object> returnMap=new HashMap<>();
            returnMap.put("bpmnModel", cscpProc);
            returnMap.put("dataList",taskDataList);
            return new ResponseBO<>(returnMap,SysErrEnum.SUCCESS);
        } catch (Exception e){
            return new ResponseBO<>(e);
        }
    }

    /**
     *审批日志列表
     * @param bid
     * @return
     */
    @InterceptRequest
    @GetMapping("/getAuditLogList/{bid}")
    public ResponseBO<List<CscpAuditLog>> getAuditLogList(@PathVariable String bid) {
        try {
            CscpProcBase cscpProcBase=cscpProcBaseService.getProcBaseByBid(bid);
            if(cscpProcBase==null){
                throw new CustomException("bid关联的业务数据不存在");
            }
            List<CscpAuditLog> cscpAuditLogs = cscpAuditLogService.getAuditLogList(cscpProcBase.getProcInstId()); //cscpAuditContentService.getAuditContentList(PROCESS_INSTANCE_ID);
            return new ResponseBO<>(cscpAuditLogs, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("getActSet Exception",e);
            return new ResponseBO<>(e);
        }
    }

    /**
     *审批意见列表
     * @param bid
     * @return
     */
    @InterceptRequest
    @GetMapping("/getAuditContentList/{bid}")
    public ResponseBO<List<CscpAuditContent>> getAuditContentList(@PathVariable String bid) {
        try {
            CscpProcBase cscpProcBase=cscpProcBaseService.getProcBaseByBid(bid);
            if(cscpProcBase==null){
                throw new CustomException("bid关联的业务数据不存在");
            }
            List<CscpAuditContent> cscpAuditContents = cscpAuditContentService.getAuditContentList(cscpProcBase.getProcInstId());
            return new ResponseBO<>(cscpAuditContents, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("getActSet Exception",e);
            return new ResponseBO<>(e);
        }
    }
    @InterceptRequest
    @PostMapping("/updateProcBase")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新进程基础procbase")
    public ResponseBO<Void> getAuditContentList(@RequestBody InterfacesParams interfacesParams) {
        try {
            if(StringUtils.isEmpty(interfacesParams.getBid())){
                throw new CustomException("bid参数不存在");
            }
            CscpProcBase cscpProcBase=cscpProcBaseService.getProcBaseByBid(interfacesParams.getBid());
            if(cscpProcBase==null){
                throw new CustomException("bid关联的业务数据不存在");
            }
            if(StringUtils.isNotEmpty(interfacesParams.getFormMainName())){
                cscpProcBase.setTitle(interfacesParams.getFormMainName());
            }
            cscpProcBaseService.updateById(cscpProcBase);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            return new ResponseBO<>(e);
        }
    }

    @InterceptRequest
    @ApiOperation(value = "根据流程ID，获取流程定义信息，包括流程环节列表、流程连线线列表")
    @GetMapping("/getFullProcessData/{bid}")
    public ResponseBO<ProcessDataDTO> getFullProcessData(@ApiParam(required = true, value = "业务ID") @PathVariable String bid) {
        try {
            if(StringUtils.isEmpty(bid)){
                throw new CustomException("bid参数不存在");
            }
            CscpProcBase cscpProcBase=cscpProcBaseService.getProcBaseByBid(bid);
            if(cscpProcBase==null){
                throw new CustomException("bid关联的业务数据不存在");
            }

            CscpProc cscpProc =activitiBpmnDefinitionService.getBpmnModelByProcessDefinitionId(cscpProcBase.getProcDefId());

            return new ResponseBO<>(activitiBpmnDefinitionService.getFullProcessData(cscpProc.getProcessDefinitionKey()),SysErrEnum.SUCCESS);
        }catch (Exception e){
            return new ResponseBO<>(e);
        }

    }
}
