package com.ctsi.interfaces.service;

import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.activiti.core.vo.DetailTaskData;
import com.ctsi.activiti.core.vo.ProcessData;
import com.ctsi.interfaces.domain.InterfacesParams;
import com.ctsi.ssdc.model.PageResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface CscpInterfacesService {

    ResponseBO<List<Map<String,Object>>> getFormFileList(String formId, String formDataId);

    void downloadFormFile(String id, HttpServletResponse response, HttpServletRequest request);

    PageResult<DetailTaskData> getActivelyTaskList(InterfacesParams interfacesParams, List<String> listAssignee) throws Exception;

    PageResult<ProcessData> getFinishTaskList(InterfacesParams interfacesParams);

    PageResult<DetailTaskData> getTodoTaskListByProcess(int pageNumber, int pageSize, String processInstanceId, String processDefinitionKey);

    PageResult<ProcessData> getFinishTaskListByProcess(int pageNumber, int pageSize, String processInstanceId, String processDefinitionKey);

    Map<String, Object> getProcessStatus(CscpProcBase cscpProcBase);
}
