package com.ctsi.interfaces.service.impl;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.bpmn.entity.CscpProc;
import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.activiti.bpmn.service.ActivitiBpmnDefinitionService;
import com.ctsi.activiti.config.ActivitiValueConfig;
import com.ctsi.activiti.core.model.TaskQueryParam;
import com.ctsi.activiti.core.vo.DetailTaskData;
import com.ctsi.activiti.core.vo.ProcessData;
import com.ctsi.activiti.core.vo.TaskData;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.domain.CscpProcTaskExtend;
import com.ctsi.business.domain.CscpProcType;
import com.ctsi.business.repository.CscpProcTypeRepository;
import com.ctsi.business.service.CscpActivitiService;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.business.service.CscpProcTaskExtendService;
import com.ctsi.common.bo.PageActiviti;
import com.ctsi.common.bo.PageForm;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.common.exception.CustomException;
import com.ctsi.common.utils.ActivitiUtils;
import com.ctsi.common.utils.JsonUtils;
import com.ctsi.common.utils.RequestParamMd5Util;
import com.ctsi.interfaces.domain.InterfacesParams;
import com.ctsi.interfaces.service.CscpInterfacesService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.DateUtil;
import com.ctsi.ssdc.util.HttpClientUtil;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricTaskInstanceQuery;
import org.activiti.engine.impl.persistence.entity.HistoricProcessInstanceEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

@Service
public class CscpInterfacesServiceImpl implements CscpInterfacesService {

    @Autowired
    private CscpProcBaseService cscpProcBaseService;



    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private CscpProcTaskExtendService cscpProcTaskExtendService;

    @Autowired
    private CscpProcTypeRepository cscpProcTypeRepository;

    @Autowired
    private RuntimeService runtimeService;


    @Autowired
    private ActivitiBpmnDefinitionService activitiBpmnDefinitionService;

    @Autowired
    private CscpActivitiService cscpActivitiService;
    @Autowired
    private CscpUserService cscpUserService;
    @Override
    public ResponseBO getFormFileList(String formId, String formDataId) {
        try {
            String timeStamp=System.currentTimeMillis()+"";
            String md5AfterValue = RequestParamMd5Util.findMd5Value(timeStamp, "", "", ActivitiValueConfig.getFormSecretKey());

            Map<String,String> headers=new HashMap<>();
            headers.put("secretTime",timeStamp);
            headers.put("secretSign",md5AfterValue);
            headers.put("secretDefinite","");

            Map<String,String> params=new HashMap<>();
            params.put("formId",formId);
            params.put("formDataId",formDataId);

            String result= HttpClientUtil.doGetAddHeaderJson(ActivitiValueConfig.getChargevulServerCformUrl()+"/api/External/getFormFiles",params,headers,"UTF-8");
            if(StringUtils.isEmpty(result)){
                throw  new CustomException("获取表单附件列表失败");
            }
            return JsonUtils.jsonToPojo(result,ResponseBO.class);
        } catch (Exception e){
            return new ResponseBO<>(e);
        }
    }

    @Override
    public void downloadFormFile(String id, HttpServletResponse response, HttpServletRequest request) {
        try {
            String timeStamp=System.currentTimeMillis()+"";
            String md5AfterValue = RequestParamMd5Util.findMd5Value(timeStamp, "", "", ActivitiValueConfig.getFormSecretKey());

            Map<String,String> headers=new HashMap<>();
            headers.put("secretTime",timeStamp);
            headers.put("secretSign",md5AfterValue);
            headers.put("secretDefinite","");

            Map<String,String> params=new HashMap<>();
            params.put("id",id);
            StringBuffer urlString=new StringBuffer();
            urlString.append(ActivitiValueConfig.getChargevulServerCformUrl()+"/api/External/downloadFormFiles?id="+id+"&secretTime="+timeStamp+"&secretSign="+md5AfterValue+"&secretDefinite=");
            // 构造URL
            URL url = new URL(urlString.toString());
            // 打开连接
            URLConnection con = url.openConnection();
            //设置请求超时为5s
            con.setConnectTimeout(10*1000);
            // 输入流
            InputStream is = con.getInputStream();

            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            byte[] buff = new byte[1024];
            int rc = 0;
            while ((rc = is.read(buff, 0, 100)) > 0) {
                swapStream.write(buff, 0, rc);
            }
            byte[] in2b = swapStream.toByteArray();
            String fileName=request.getParameter("fileName");
            if(StringUtils.isEmpty(fileName)){
                fileName= UUID.randomUUID().toString();
            }

            response.setHeader("Content-Disposition", "attachment;filename"+fileName);
            response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
            response.getOutputStream().write(in2b);
        } catch (RuntimeException | IOException e){
            e.printStackTrace();
            throw new CustomException(e.getMessage());
        }
    }

    @Override
    public PageResult<DetailTaskData> getActivelyTaskList(InterfacesParams interfacesParams, List<String> listAssignee) throws Exception {
        TaskQueryParam taskForQuery = new TaskQueryParam();

        if (ActivitiUtils.isNotEmpty(interfacesParams.getProcessDefinitionKey())) {
            taskForQuery.setProcessDefinitionKey(interfacesParams.getProcessDefinitionKey());
        }

        if (ActivitiUtils.isNotEmpty(interfacesParams.getProcessInstanceId())) {
            taskForQuery.setProcessInstanceId(interfacesParams.getProcessInstanceId());
        }

        if (ActivitiUtils.isNotEmpty(interfacesParams.getProcessName())) {
            taskForQuery.setProcessName("%" + interfacesParams.getProcessName() + "%");
        }


        // 受理人、组任务
        taskForQuery.setTaskCandidateOrAssignedIn(listAssignee);

      /*  Map<String,Object> params=new HashMap<>();
        params.put("appCode",interfacesParams.getAppCode());
        if(StringUtils.isNotEmpty(interfacesParams.getTypeId())){
            params.put("typeId",interfacesParams.getTypeId());
        }*/
        LambdaQueryWrapper<CscpProcType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcType::getAppCode,interfacesParams.getAppCode())
                .eq(StringUtils.isNotEmpty(interfacesParams.getTypeId()),CscpProcType::getTypeId, interfacesParams.getTypeId());
        List<CscpProcType> typeList= cscpProcTypeRepository.selectList(lambdaQueryWrapper);

        List<String> typeList2=new ArrayList<>();
        for(CscpProcType cscpProcType: typeList){
            typeList2.add(cscpProcType.getTypeId());
        }

        if(typeList2.size() == 0){
            typeList2.add("~");//防止因无数据导致全部查出
        }

        LambdaQueryWrapper<CscpProc> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CscpProcType::getTypeId,typeList2);

        List<CscpProc> listCscpProc  = cscpActivitiService.list(lambdaQueryWrapper1);

        Set<String> setDefKey = new HashSet<>(listCscpProc.size());

        for(CscpProc model : listCscpProc){
            String pid = model.getProcessDefinitionKey();
            setDefKey.add(pid);
        }

        List<String> listDefKey = new ArrayList<>(setDefKey.size());
        listDefKey.addAll(setDefKey);

        if(listDefKey.size() == 0){
            listDefKey.add("~");//防止因无数据导致全部查出
        }

        taskForQuery.setProcessDefinitionKeyIn(listDefKey);

        //拼接搜索条件
        if(StringUtils.isNotEmpty(interfacesParams.getFormMainName())){
            taskForQuery.setTitle("%" + interfacesParams.getFormMainName()+"%");
        }
       /* if(StringUtils.isNotEmpty(interfacesParams.getExt1())){
            taskForQuery.setExt1(interfacesParams.getExt1());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt2())){
            taskForQuery.setExt2(interfacesParams.getExt2());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt3())){
            taskForQuery.setExt3(interfacesParams.getExt3());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt4())){
            taskForQuery.setExt4(interfacesParams.getExt4());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt5())){
            taskForQuery.setExt5(interfacesParams.getExt5());
        }*/


        PageForm<TaskQueryParam> form = new PageForm<>();
        form.setPageSize(interfacesParams.getPageSize());
        form.setCurrentPage(interfacesParams.getPageNumber()+1);//pageNumber是0开始,currentPage要求从1开始
        form.setT(taskForQuery);
        PageActiviti<TaskEntityImpl> pr =  cscpActivitiService.getActivitiTaskListMultiAssignForPage(form);
        List<TaskEntityImpl> list = pr.getData();
        long count = pr.getTotal();

        // 转换任务信息
        List<DetailTaskData> taskDataList = new ArrayList<>();
        if (list != null) {
            list.forEach(task -> {
                TaskData taskData = TaskData.newInstance(task);
                if(taskData == null){
                    return;
                }
                DetailTaskData detailTaskData = ActivitiUtils.toBean(DetailTaskData.class, taskData);
                // 获取历史流程实例
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
                ProcessData processData = ProcessData.newInstance(historicProcessInstance);
                CscpProcBase cscpProcBase = cscpProcBaseService.findProcBaseGeneral(task.getProcessInstanceId(), task.getProcessDefinitionId());
                if(cscpProcBase!=null){
                    detailTaskData.setProcess(processData);
                    detailTaskData.setTaskTitle(cscpProcBase.getTitle());
                    detailTaskData.setFormDataId(cscpProcBase.getFormDataId());
                    detailTaskData.setFormMainName(cscpProcBase.getTitle());
                    detailTaskData.setProcessCreatTime(cscpProcBase.getCreateTime());
                    detailTaskData.setFormId(cscpProcBase.getFormDefId());
                    detailTaskData.setCscpProcBase(cscpProcBase);
                }
                detailTaskData.setStartTime(DateUtil.parseDate3(taskData.getStartTime()));
                taskDataList.add(detailTaskData);
            });
        }
        return new PageResult(taskDataList, count, count);
    }

    @Override
    public PageResult<DetailTaskData> getTodoTaskListByProcess(int pageNumber, int pageSize, String processInstanceId, String processDefinitionKey) {
        TaskQuery taskQuery = taskService.createTaskQuery();
        taskQuery.processDefinitionKey(processDefinitionKey);
        taskQuery.processInstanceId(processInstanceId);
        List<Task> list = taskQuery.orderByTaskCreateTime().active().desc().listPage(pageNumber * pageSize, pageSize);
        long count = taskQuery.count();

        // 转换任务信息
        List<DetailTaskData> taskDataList = new ArrayList<>();
        if (list != null) {
            list.forEach(task -> {
                TaskData taskData = TaskData.newInstance(task);
                if(taskData == null){
                    return;
                }
                DetailTaskData detailTaskData = ActivitiUtils.toBean(DetailTaskData.class, taskData);
                // 获取历史流程实例
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
                ProcessData processData = ProcessData.newInstance(historicProcessInstance);
                CscpProcBase cscpProcBase = cscpProcBaseService.findProcBaseGeneral(task.getProcessInstanceId(), task.getProcessDefinitionId());
                if(cscpProcBase!=null){
                    detailTaskData.setProcess(processData);
                    detailTaskData.setTaskTitle(cscpProcBase.getTitle());
                    detailTaskData.setFormDataId(cscpProcBase.getFormDataId());
                    detailTaskData.setFormMainName(cscpProcBase.getTitle());
                    detailTaskData.setProcessCreatTime(cscpProcBase.getCreateTime());
                    detailTaskData.setFormId(cscpProcBase.getFormDefId());
                }
                //获取开始时间和结束时间
                /*CscpProcTaskExtendExample cscpProcTaskExtendExample=new CscpProcTaskExtendExample();
                CscpProcTaskExtendExample.Criteria criteria=cscpProcTaskExtendExample.createCriteria();
                criteria.andProcinstidEqualTo(taskData.getProcessInstanceId());
                criteria.andProcdefidEqualTo(cscpProcBase.getProcDefId());
                criteria.andTaskidEqualTo(taskData.getTaskId());*/
                LambdaQueryWrapper<CscpProcTaskExtend> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(CscpProcTaskExtend::getProcinstId,taskData.getProcessInstanceId())
                        .eq(CscpProcTaskExtend::getProcdefId,cscpProcBase.getProcDefId())
                        .eq(CscpProcTaskExtend::getTaskId,taskData.getTaskId());

                CscpProcTaskExtend cscpProcTaskExtend=cscpProcTaskExtendService.getOne(lambdaQueryWrapper);
                if(cscpProcTaskExtend!=null){
                    String format = cn.hutool.core.date.DateUtil.format(cscpProcTaskExtend.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN);
                    detailTaskData.setStartTime(DateUtil.parseDate3(format));
                    if(StringUtils.isNotEmpty(cscpProcTaskExtend.getEndTime())){
                        detailTaskData.setEndTime(DateUtil.parseDate3(cscpProcTaskExtend.getEndTime()));
                    }
                }

                List<IdentityLink> identityLinkList=runtimeService.getIdentityLinksForProcessInstance(taskData.getProcessInstanceId());
                for (IdentityLink identityLink : identityLinkList){
                    if(taskData.getTaskId().equals(identityLink.getTaskId())){
                        detailTaskData.setAssignee(identityLink.getUserId());
                        break;
                    }
                }

                if (ActivitiUtils.isNotEmpty(taskData.getAssignee())) {
                    CscpUserDTO CscpUserDTO=cscpUserService.findByUserId(SecurityUtils.getCurrentUserId());
                    taskData.setAssigneeName(CscpUserDTO.getRealName());
                }
                taskDataList.add(detailTaskData);
            });
        }
        return new PageResult(taskDataList, count, count);
    }

    @Override
    public PageResult<ProcessData> getFinishTaskListByProcess(int pageNumber, int pageSize, String processInstanceId, String processDefinitionKey) {
        List<ProcessData> dataList = new ArrayList<>();
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
        historicTaskInstanceQuery.processInstanceId(processInstanceId);
        historicTaskInstanceQuery.processDefinitionKey(processDefinitionKey);
        historicTaskInstanceQuery.finished();
        historicTaskInstanceQuery.orderByTaskCreateTime().desc();
        List<HistoricTaskInstance> data = historicTaskInstanceQuery.listPage(pageNumber * pageSize, pageSize);
        Long total = historicTaskInstanceQuery.count();
        if (data != null && data.size() > 0) {
            data.forEach(rel -> {
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(rel.getProcessInstanceId()).singleResult();
                HistoricProcessInstanceEntity historicProcessInstanceTemp = (HistoricProcessInstanceEntity)historicProcessInstance;
                CscpProcBase cscpProcBase = cscpProcBaseService.findProcBaseGeneral(historicProcessInstanceTemp.getProcessInstanceId(), historicProcessInstanceTemp.getProcessDefinitionId());
                ProcessData processData = ProcessData.newInstance(historicProcessInstance);
                processData.setTaskTitle(cscpProcBase.getTitle());
                processData.setNodeKey(rel.getTaskDefinitionKey());
                processData.setNodeName(rel.getName());
                processData.setFormId(cscpProcBase.getFormDefId());
                processData.setFormDataId(cscpProcBase.getFormDataId());
                processData.setFormMainName(cscpProcBase.getTitle());
                processData.setProcessCreatTime(cscpProcBase.getCreateTime());
                processData.setTaskId(rel.getId());
                TaskQuery taskQuery = taskService.createTaskQuery();
                taskQuery.processInstanceId(rel.getProcessInstanceId());
                List<Task> lCurrentTask = taskQuery.orderByTaskCreateTime().desc().list();
                if(lCurrentTask != null && lCurrentTask.size() > 0){
                    processData.setCurrentNodeKey(lCurrentTask.get(0).getTaskDefinitionKey());
                    processData.setCurrentNodeName(lCurrentTask.get(0).getName());
                }
                BpmnNode bpmnNode= activitiBpmnDefinitionService.getBpmnNodeByNodeKey(cscpProcBase.getProcDefId(),rel.getTaskDefinitionKey());
                if(bpmnNode!=null){
                    processData.setFormUrl(bpmnNode.getFormUrl());
                }
                if(rel.getStartTime() != null) {
                    processData.setStartTime(rel.getStartTime());
                }
                if(rel.getEndTime() != null) {
                    processData.setEndTime(rel.getEndTime());
                }

                dataList.add(processData);
            });
        }
        return new PageResult(dataList, total, total);
    }

    @Override
    public Map<String, Object> getProcessStatus(CscpProcBase cscpProcBase) {
        Map<String, Object> processInfo=new HashMap<>();
        processInfo.put("processName","");
        processInfo.put("modelName","");
        processInfo.put("currentNodeName","");
        processInfo.put("currentNodeKey","");
        processInfo.put("createUserName","");
        processInfo.put("startTime","");
        processInfo.put("isEnd",true);
        processInfo.put("dealUserName","");
        processInfo.put("procInstId","");


        processInfo.put("processName",cscpProcBase.getTitle());
        processInfo.put("createUserName",cscpProcBase.getCreateName());
        processInfo.put("procInstId",cscpProcBase.getProcInstId());
        CscpProc cscpProc = activitiBpmnDefinitionService.getBpmnModelByProcessDefinitionId(cscpProcBase.getProcDefId());

        if(cscpProc ==null){
            throw new CustomException("流程模型不存在");
        }

        processInfo.put("modelName", cscpProc.getName());


        ProcessInstance processInstance=runtimeService.createProcessInstanceQuery().processInstanceId(cscpProcBase.getProcInstId()).processDefinitionKey(cscpProc.getProcessDefinitionKey()).singleResult();
        String taskId="";
        if(processInstance!=null){
            processInfo.put("isEnd",false);
            List<Task> taskList=taskService.createTaskQuery().processInstanceId(cscpProcBase.getProcInstId()).list();
            if(taskList!=null && taskList.size()>0){
                processInfo.put("currentNodeName",taskList.get(0).getName());
                processInfo.put("currentNodeKey",taskList.get(0).getTaskDefinitionKey());
                taskId=taskList.get(0).getId();
            }
            StringBuffer names=new StringBuffer();
            for(Task task : taskList){
                List<IdentityLink> linkList=taskService.getIdentityLinksForTask(task.getId());
                if(linkList!=null && linkList.size()>0){
                    String userId=linkList.get(0).getUserId();
                    CscpUserDTO CscpUserDTO=cscpUserService.findByUserId(Long.valueOf(userId));
                    names.append(CscpUserDTO.getRealName()+",");
                }
            }
            if(names!=null){
                processInfo.put("dealUserName",names.deleteCharAt(names.lastIndexOf(",")));
            }
        }else{
            HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
            historicTaskInstanceQuery.processInstanceId(cscpProcBase.getProcInstId());
            historicTaskInstanceQuery.processDefinitionKey(cscpProc.getProcessDefinitionKey());
            historicTaskInstanceQuery.finished();
            historicTaskInstanceQuery.orderByTaskCreateTime().desc();
            List<HistoricTaskInstance> data = historicTaskInstanceQuery.list();
            if(data!=null && data.size()>0){
                processInfo.put("currentNodeName",data.get(0).getName());
                processInfo.put("currentNodeKey",data.get(0).getTaskDefinitionKey());
                if(StringUtils.isNotEmpty(data.get(0).getAssignee())){
                    CscpUserDTO CscpUserDTO=cscpUserService.findByUserId(Long.valueOf(data.get(0).getAssignee()));
                    processInfo.put("dealUserName",CscpUserDTO.getRealName());
                }
                taskId=data.get(0).getId();
            }else {
                throw new CustomException("流程不存在");
            }
        }
      /*  CscpProcTaskExtendExample cscpProcTaskExtendExample=new CscpProcTaskExtendExample();
        CscpProcTaskExtendExample.Criteria criteria=cscpProcTaskExtendExample.createCriteria();
        criteria.andProcinstidEqualTo(cscpProcBase.getProcInstId());
        criteria.andProcdefidEqualTo(cscpProcBase.getProcDefId());
        criteria.andTaskidEqualTo(taskId);*/

        LambdaQueryWrapper<CscpProcTaskExtend> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcTaskExtend::getProcinstId,cscpProcBase.getProcInstId())
                .eq(CscpProcTaskExtend::getProcdefId,cscpProcBase.getProcDefId())
                .eq(CscpProcTaskExtend::getTaskId,taskId);

        CscpProcTaskExtend cscpProcTaskExtend=cscpProcTaskExtendService.getOne(lambdaQueryWrapper);
        if(cscpProcTaskExtend!=null){
            processInfo.put("startTime",cscpProcTaskExtend.getCreateTime());
        }
        return processInfo;
    }

    @Override
    public PageResult<ProcessData> getFinishTaskList(InterfacesParams interfacesParams) {
        List<ProcessData> dataList = new ArrayList<>();

        TaskQueryParam taskQueryParam=new TaskQueryParam();

        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
        if (ActivitiUtils.isNotEmpty(interfacesParams.getProcessDefinitionKey())) {
            //historicTaskInstanceQuery.processDefinitionKey(interfacesParams.getProcessDefinitionKey());
            taskQueryParam.setProcessDefinitionKey(interfacesParams.getProcessDefinitionKey());
        }

        if (ActivitiUtils.isNotEmpty(interfacesParams.getProcessInstanceId())) {
           // historicTaskInstanceQuery.processInstanceId(interfacesParams.getProcessInstanceId());
            taskQueryParam.setProcessInstanceId(interfacesParams.getProcessInstanceId());
        }

        if (ActivitiUtils.isNotEmpty(interfacesParams.getProcessName())) {
            //historicTaskInstanceQuery.processDefinitionNameLike("%" + interfacesParams.getProcessName() + "%");
            taskQueryParam.setProcessName("%" + interfacesParams.getProcessName() + "%");
        }
       /* Map<String,Object> params=new HashMap<>();
        params.put("appCode",interfacesParams.getAppCode());
        if(StringUtils.isNotEmpty(interfacesParams.getTypeId())){
            params.put("typeId",interfacesParams.getTypeId());
        }*/
        LambdaQueryWrapper<CscpProcType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcType::getAppCode,interfacesParams.getAppCode())
                .eq(StringUtils.isNotEmpty(interfacesParams.getTypeId()),
                        CscpProcType::getTypeId, interfacesParams.getTypeId());

        List<CscpProcType> typeList= cscpProcTypeRepository.selectList(lambdaQueryWrapper);

        List<String> typeList2=new ArrayList<>();
        for(CscpProcType cscpProcType: typeList){
            typeList2.add(cscpProcType.getTypeId());
        }

        if(typeList2.size() == 0){
            typeList2.add("~");//防止因无数据导致全部查出
        }

     /*   BpmnModelExample bpmnModelExample = new BpmnModelExample();

        BpmnModelExample.Criteria bmeCri1 = bpmnModelExample.createCriteria();
        bmeCri1.andTypeIdIn(typeList2);*/
        LambdaQueryWrapper<CscpProc> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CscpProcType::getTypeId,typeList2);

        List<CscpProc> listCscpProc  = cscpActivitiService.list(lambdaQueryWrapper1);

        Set<String> setDefKey = new HashSet<>(listCscpProc.size());

        for(CscpProc model : listCscpProc){
            String pid = model.getProcessDefinitionKey();
            setDefKey.add(pid);
        }

        List<String> listDefKey = new ArrayList<>(setDefKey.size());
        listDefKey.addAll(setDefKey);

        if(listDefKey.size() == 0){
            listDefKey.add("~");//防止因无数据导致全部查出
        }

        //historicTaskInstanceQuery.processDefinitionKeyIn(listDefKey);
        taskQueryParam.setProcessDefinitionKeyIn(listDefKey);

        if(StringUtils.isNotEmpty(interfacesParams.getUserId())){
            //historicTaskInstanceQuery.taskAssignee(interfacesParams.getUserId());
            taskQueryParam.setTaskCandidateOrAssignedInStr(interfacesParams.getUserId());
        }

        //拼接搜索条件
        if(StringUtils.isNotEmpty(interfacesParams.getFormMainName())){
            taskQueryParam.setTitle("%" + interfacesParams.getFormMainName()+"%");
        }
       /* if(StringUtils.isNotEmpty(interfacesParams.getExt1())){
            taskQueryParam.setExt1(interfacesParams.getExt1());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt2())){
            taskQueryParam.setExt2(interfacesParams.getExt2());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt3())){
            taskQueryParam.setExt3(interfacesParams.getExt3());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt4())){
            taskQueryParam.setExt4(interfacesParams.getExt4());
        }
        if(StringUtils.isNotEmpty(interfacesParams.getExt5())){
            taskQueryParam.setExt5(interfacesParams.getExt5());
        }*/

        PageForm<TaskQueryParam> form = new PageForm<>();
        form.setPageSize(interfacesParams.getPageSize());
        form.setCurrentPage(interfacesParams.getPageNumber()+1);//pageNumber是0开始,currentPage要求从1开始
        form.setT(taskQueryParam);
        PageActiviti<HistoricTaskInstance> pr =  cscpActivitiService.getActivitiFinishedTaskListForPage(form);
        List<HistoricTaskInstance> data = pr.getData();
        long total = pr.getTotal();
/*
        historicTaskInstanceQuery.finished();
        historicTaskInstanceQuery.orderByTaskCreateTime().desc();

        int pageNumber=interfacesParams.getPageNumber();
        int pageSize=interfacesParams.getPageSize();

        List<HistoricTaskInstance> data = historicTaskInstanceQuery.listPage(pageNumber * pageSize, pageSize);
        Long total = historicTaskInstanceQuery.count();*/
        if (data != null && data.size() > 0) {
            data.forEach(rel -> {
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(rel.getProcessInstanceId()).singleResult();
                HistoricProcessInstanceEntity historicProcessInstanceTemp = (HistoricProcessInstanceEntity)historicProcessInstance;
                CscpProcBase cscpProcBase = cscpProcBaseService.findProcBaseGeneral(historicProcessInstanceTemp.getProcessInstanceId(), historicProcessInstanceTemp.getProcessDefinitionId());
                ProcessData processData = ProcessData.newInstance(historicProcessInstance);
                processData.setTaskTitle(cscpProcBase.getTitle());
                processData.setNodeKey(rel.getTaskDefinitionKey());
                processData.setNodeName(rel.getName());
                processData.setFormId(cscpProcBase.getFormDefId());
                processData.setFormDataId(cscpProcBase.getFormDataId());
                processData.setFormMainName(cscpProcBase.getTitle());
                processData.setProcessCreatTime(cscpProcBase.getCreateTime());
                processData.setTaskId(rel.getId());
                processData.setCscpProcBase(cscpProcBase);
                TaskQuery taskQuery = taskService.createTaskQuery();
                taskQuery.processInstanceId(rel.getProcessInstanceId());
                List<Task> lCurrentTask = taskQuery.orderByTaskCreateTime().desc().list();
                if(lCurrentTask != null && lCurrentTask.size() > 0){
                    processData.setCurrentNodeKey(lCurrentTask.get(0).getTaskDefinitionKey());
                    processData.setCurrentNodeName(lCurrentTask.get(0).getName());
                }
                BpmnNode bpmnNode=activitiBpmnDefinitionService.getBpmnNodeByNodeKey(cscpProcBase.getProcDefId(),rel.getTaskDefinitionKey());
                if(bpmnNode!=null){
                    processData.setFormUrl(bpmnNode.getFormUrl());
                }
                if(rel.getStartTime() != null) {
                    processData.setStartTime(rel.getStartTime());
                }
                if(rel.getEndTime() != null) {
                    processData.setEndTime(rel.getEndTime());
                }
                dataList.add(processData);
            });
        }
        return new PageResult(dataList, total, total);
    }
}
