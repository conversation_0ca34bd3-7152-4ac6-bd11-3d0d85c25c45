package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.activiti.core.vo.PageQuery;

import java.util.List;

import com.ctsi.business.domain.CscpInformation;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.repository.BaseRepository;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface CscpInformationRepository extends MybatisBaseMapper<CscpInformation> {



}