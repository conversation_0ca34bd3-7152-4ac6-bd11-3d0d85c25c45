package com.ctsi.business.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.activiti.core.model.TaskQueryParam;
import com.ctsi.activiti.core.vo.TaskContentVO;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.dto.QueryAllProcessMonitorDTO;
import com.ctsi.business.dto.QueryApproveManagementCodeDTO;
import com.ctsi.business.dto.QuerySmsNoticeListDTO;
import com.ctsi.business.vo.QueryAllProcessMonitorVO;
import com.ctsi.business.vo.QueryApproveManagementCodeVO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface CscpProcBaseRepository extends MybatisBaseMapper<CscpProcBase> {

    IPage<CscpProcBase> queryPageCscpProcBaseForPage(@Param("page") Page pagepq,@Param("obj") CscpProcBase cscpProcBase);

    /**
     * 获取我经办过的数据
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getUserApproveProcessList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    /**
     * 获取在办列表的数据
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getHandleUserProcessList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    /**
     * 获取我已办过的数据
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getUserEndProcessList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    /**
     * 已办件动态
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskContentVO> queryDoneApprovalList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    /**
     * 联络员获取领导用户已办结过的流程列表
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getLiaisonUserEndProcessList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    /**
     * 一键办结列表查询
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getOneClickFinishProcessList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getLiaisonActivelyTaskListUnionOutAuth(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    @InterceptorIgnore(tenantLine="true")
    List<TaskVO> getLiaisonActivelyTaskListUnionOutAuthSubscript(@Param("obj") TaskQueryParam taskQueryParam);

    /**
     * 获取书记的待办列表
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getSecretaryActivelyTaskListUnionOutAuth(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    /**
     * 获取待办在办已办三种状态列表的数据
     * @param iPage
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<TaskVO> getUserProcessList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    /**
     * 获取监控
     * @param
     * @param taskQueryParam
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<TaskVO> getProcessMonitorList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    @InterceptorIgnore(tenantLine = "true")
    IPage<QueryAllProcessMonitorDTO> queryAllProcessMonitorList(@Param("page")IPage iPage, @Param("vo") QueryAllProcessMonitorVO vo);

    @InterceptorIgnore(tenantLine = "true")
    List<QuerySmsNoticeListDTO> querySmsNoticeList(@Param("taskIdList") List<Long> taskIdList);

    @InterceptorIgnore(tenantLine = "true")
    IPage<TaskVO> getCompanyMonitorProcessList(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam);

    @InterceptorIgnore(tenantLine = "true")
    void updateIsCircularizeByProcInstId(@Param("procInstId") String procInstId,@Param("reviewComments") String reviewComments);

    @InterceptorIgnore(tenantLine = "true")
    IPage<HashMap<String, Object>> getMyAllBusinessCscpProcPage(@Param("page")IPage iPage, @Param("obj")TaskQueryParam taskQueryParam,
                                                                @Param("processDefinitionIdList") List<String> processDefinitionIdList);

    /**
     * 获取某部门下不同收文类型已发布最大编号
     *
     * @param departmentId 部门ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param receiveType 收文类型
     * @return 响应参数
     */
    @InterceptorIgnore(tenantLine = "true")
    String getApproveManagementCode(@Param("departmentId") Long departmentId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("receiveType") String receiveType);


    /**
     * 获取某部门下不同收文类型已发布最大内部编号
     *
     * @param departmentId 部门ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param receiveType 收文类型
     * @return 响应参数
     */
    @InterceptorIgnore(tenantLine = "true")
    String getApproveInnerCode(@Param("departmentId") Long departmentId,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime,
                               @Param("formId") String formId,
                               @Param("receiveType") String receiveType);

    /**
     * 获取收文呈批件，指定收文类型历史最大文号
     * @param vo
     * @param startTime
     * @param endTime
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    String selectRecApprovalMaxCode(@Param("vo") QueryApproveManagementCodeVO vo,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime
                                               );

    /**
     * 检查文号是否重复，重复则返回最近已用10个文号，按时间降序排序
     * @param vo
     * @param startTime
     * @param endTime
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> checkOfficialDocumentNumber(@Param("vo") QueryApproveManagementCodeVO vo,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime
                                               );

    /**
     * 获取某部门下不同收文类型已发布后3位最大编号
     * @param departmentId
     * @param startTime
     * @param endTime
     * @param receiveType
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    String getMaxLastThreeApproveManagementCode(@Param("departmentId") Long departmentId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime,
                                                @Param("formId") String formId,
                                                @Param("receiveType") String receiveType);


    @InterceptorIgnore(tenantLine = "true")
    List<QueryApproveManagementCodeDTO> queryApproveManagementCode(@Param("departmentId") Long departmentId, @Param("type") Integer type);

    /**
     * 获取呈批件 流程详情信息
     * @param process_instance_id
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    TaskVO getProcessInfoByInstanceId(@Param("instanceId") String process_instance_id);

    /**
     * 获取上一个历史流程节点
     * @return
     * @param processInstanceId
     */

    @Select({
            "SELECT \n" +
                    "  procinst_id, \n" +
                    "  user_id, \n" +
                    "  a.create_name createName \n" +

                    " FROM " +
                    "  cscp_proc_task_extend a \n" +
                    " WHERE a.deleted = 0 \n" +
                    "  AND procinst_id = #{processInstanceId} " +

                    " ORDER BY create_time DESC    limit 1 "
    })
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Object> queryTaskNodeByNextUserAndInstanceId(@Param("processInstanceId") String processInstanceId);

    @InterceptorIgnore(tenantLine = "true")
    Integer changePrintDownloadStatus(@Param("processInstanceId")String processInstanceId, @Param("isPrint")String isPrint,
                                      @Param("isPrintName")String isPrintName, @Param("userId")Long userId);

    /*
    * 三服务-查询当前用户待办呈批件数量
    * */
    @InterceptorIgnore(tenantLine = "true")
    Long countHandlerUserApproval(@Param("userId") Long userId,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    /*
    * 三服务-查询当前用户待阅信息传阅数量
    * */
    @InterceptorIgnore(tenantLine = "true")
    Long countHandlerUserCirclate(@Param("userId") Long userId,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    /*
     * 查询是否存在转办子呈批件，子呈批件是否存在
     * */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpProcBase> selectForwardProcBase(@Param("forwardProcessInstanceId") String forwardProcessInstanceId);

    /*
     * 查询是否存在转办子呈批件，子呈批件是否存在
     * */
    @InterceptorIgnore(tenantLine = "true")
    int deleteForwardRecordByprocessInstanceId(@Param("processInstanceId") String processInstanceId);

    @InterceptorIgnore(tenantLine = "true")
    Map<String,Object> selectByExaminationId(@Param("examinationId")String examinationId);

    @InterceptorIgnore(tenantLine = "true")
    boolean updateCpbIdById(@Param("cpbId")String cpbId,@Param("id")String id);

    @InterceptorIgnore(tenantLine = "true")
    boolean updateFormDataId(@Param("newFormDateId")String newFormDateId,@Param("oldFormDateId")String oldFormDateId);

    @InterceptorIgnore(tenantLine = "true")
    void saveProcessInstanceId(@Param("formDataId") String formDataId, @Param("processInstanceId") String processInstanceId);

    @InterceptorIgnore(tenantLine = "true")
    void updateSbbaFormDataId(@Param("newFormDateId")String newFormDateId,@Param("oldFormDateId")String oldFormDateId);

    @InterceptorIgnore(tenantLine = "true")
    void saveSbbaProcessInstanceId(@Param("formDataId") String formDataId, @Param("processInstanceId") String processInstanceId);
}
