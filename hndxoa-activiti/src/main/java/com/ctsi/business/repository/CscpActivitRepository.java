package com.ctsi.business.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.activiti.bpmn.dto.CscpProcDTO;
import com.ctsi.activiti.bpmn.entity.CscpProc;
import com.ctsi.activiti.bpmn.entity.CscpProcExtended;
import com.ctsi.activiti.bpmn.entity.TestEntity;
import com.ctsi.activiti.core.model.TaskQueryParam;
import com.ctsi.activiti.core.vo.PageQuery;
import com.ctsi.business.domain.Cscp;
import com.ctsi.common.utils.PageData;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

@Mapper
public interface CscpActivitRepository extends MybatisBaseMapper<CscpProc> {

    /**
     * 选择子流程时不按照单位隔离，按照租户隔离，顾调用时按照不同的隔离方式自己加条件
     * @param iPage
     * @param cscpProc
     * @return
     */
    @InterceptorIgnore(others = "tenantId@true")
    IPage<CscpProc> queryPageModelList(IPage<CscpProc> iPage, @Param("obj") CscpProc cscpProc);

    /**
     * 查询部署的最新的流程图
     * @param iPage
     * @param cscpProc
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<CscpProc> queryPageModelListNoAdd(IPage<CscpProc> iPage, @Param("obj") CscpProc cscpProc);

    /**
     * 根据当前租户及其所有子租户过滤
     * @param iPage
     * @param cscpProcDTO
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<CscpProcDTO> queryPageModelListBytenantIdList(IPage<CscpProcDTO> iPage, @Param("obj") CscpProcDTO cscpProcDTO);

    List<CscpProcExtended> queryPageModelExtendedList(PageQuery pq) throws Exception;

    IPage<PageData> queryPageCancellationProcessList(IPage<PageData> iPage, @Param("obj") Cscp pq);

    IPage<PageData> queryPageViewTaskList(IPage<PageData> iPage, @Param("obj") Cscp pq);

    List<PageData> getExpressionByNode(PageData pageData);

    void viewTask(PageData pageData);

    void setViewTaskRead(PageData pageData);

    PageData getViewTask(PageData pd);

    IPage<PageData> queryPageAllTaskList(IPage page, @Param("obj") Cscp pq);

    IPage<PageData> queryPageOverTimeTask(IPage page, @Param("obj") Cscp pq);

    Integer queryProcessGraphCount(Map<String, Object> params);

    Integer queryProcessPieCount(Map<String, Object> params);


    void updateActHiActinst(@Param("processInstanceId") String processInstanceId, @Param("id") String id);

    IPage<TaskEntityImpl> queryPageTaskListMultiAssign(IPage page, @Param("obj") TaskQueryParam taskQueryParam);

    List<HistoricTaskInstance> queryPageFinishedTaskListForPage(PageQuery pq);


}
