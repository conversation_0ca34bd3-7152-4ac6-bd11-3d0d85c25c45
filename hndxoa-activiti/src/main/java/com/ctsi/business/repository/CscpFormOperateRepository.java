package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.business.domain.CscpFormOperate;
import com.ctsi.business.domain.CscpFormOperateExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpFormOperateRepository extends BaseMapper<CscpFormOperate> {
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    long countByExample(CscpFormOperateExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByExample(CscpFormOperateExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insert(CscpFormOperate record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insertSelective(CscpFormOperate record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    List<CscpFormOperate> selectByExample(CscpFormOperateExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    CscpFormOperate selectByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExampleSelective(@Param("record") CscpFormOperate record, @Param("example") CscpFormOperateExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExample(@Param("record") CscpFormOperate record, @Param("example") CscpFormOperateExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKeySelective(CscpFormOperate record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPERATE
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKey(CscpFormOperate record);
}