package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.business.domain.CscpFormOption;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpFormOptionRepository extends BaseMapper<CscpFormOption> {
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    long countByExample(CscpFormOptionExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByExample(CscpFormOptionExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insert(CscpFormOption record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insertSelective(CscpFormOption record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    List<CscpFormOption> selectByExampleWithBLOBs(CscpFormOptionExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    List<CscpFormOption> selectByExample(CscpFormOptionExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    CscpFormOption selectByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExampleSelective(@Param("record") CscpFormOption record, @Param("example") CscpFormOptionExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExampleWithBLOBs(@Param("record") CscpFormOption record, @Param("example") CscpFormOptionExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExample(@Param("record") CscpFormOption record, @Param("example") CscpFormOptionExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKeySelective(CscpFormOption record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKeyWithBLOBs(CscpFormOption record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_FORM_OPTION
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKey(CscpFormOption record);
}