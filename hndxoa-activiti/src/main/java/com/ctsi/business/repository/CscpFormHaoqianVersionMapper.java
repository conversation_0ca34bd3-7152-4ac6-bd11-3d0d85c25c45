package com.ctsi.business.repository;


import com.ctsi.business.domain.CscpFormHaoqianVersion;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 好签表单版本表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Mapper
public interface CscpFormHaoqianVersionMapper extends MybatisBaseMapper<CscpFormHaoqianVersion> {

    void deleteByTaskId(@Param("procInstId")String procInstId,@Param("taskId")String taskId);
}
