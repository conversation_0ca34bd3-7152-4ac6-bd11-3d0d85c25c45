package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.business.domain.CscpProcTaskExtend;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpProcTaskExtendRepository extends MybatisBaseMapper<CscpProcTaskExtend> {

    void deleteByprocessInstanceIdNew(@Param("processInstanceId")String processInstanceId);
}