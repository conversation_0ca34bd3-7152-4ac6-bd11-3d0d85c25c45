package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.activiti.core.vo.PageQuery;
import com.ctsi.business.domain.CscpHoliday;
import java.util.List;

import com.ctsi.common.utils.PageData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpHolidayRepository extends BaseMapper<CscpHoliday> {
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    long countByExample(CscpHolidayExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int deleteByExample(CscpHolidayExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int deleteByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int insert(CscpHoliday record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int insertSelective(CscpHoliday record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    CscpHoliday selectByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int updateByExampleSelective(@Param("record") CscpHoliday record, @Param("example") CscpHolidayExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int updateByExample(@Param("record") CscpHoliday record, @Param("example") CscpHolidayExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int updateByPrimaryKeySelective(CscpHoliday record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_holiday
//     *
//     * @mbg.generated Tue Mar 31 09:25:35 CST 2020
//     */
//    int updateByPrimaryKey(CscpHoliday record);

    List<PageData> queryPageOvertimeTask(PageQuery pageQuery);

    List<PageData> queryPageOvertimeTaskHistory(PageQuery pageQuery);
    //TODO 改
}