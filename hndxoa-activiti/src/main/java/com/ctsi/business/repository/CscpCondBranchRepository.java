package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.business.domain.CscpCondBranch;
import com.ctsi.business.domain.CscpCondBranchExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpCondBranchRepository extends BaseMapper<CscpCondBranch> {
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    long countByExample(CscpCondBranchExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByExample(CscpCondBranchExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insert(CscpCondBranch record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insertSelective(CscpCondBranch record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    List<CscpCondBranch> selectByExampleWithBLOBs(CscpCondBranchExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    List<CscpCondBranch> selectByExample(CscpCondBranchExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    CscpCondBranch selectByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExampleSelective(@Param("record") CscpCondBranch record, @Param("example") CscpCondBranchExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExampleWithBLOBs(@Param("record") CscpCondBranch record, @Param("example") CscpCondBranchExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExample(@Param("record") CscpCondBranch record, @Param("example") CscpCondBranchExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKeySelective(CscpCondBranch record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKeyWithBLOBs(CscpCondBranch record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_COND_BRANCH
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKey(CscpCondBranch record);
}