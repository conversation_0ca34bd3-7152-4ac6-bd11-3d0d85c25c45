package com.ctsi.business.repository;

import java.util.List;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.business.domain.CscpOpinion;
import com.ctsi.ssdc.repository.BaseRepository;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpOpinionRepository extends BaseMapper<CscpOpinion> {


    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpOpinion> getOpinionPageList(IPage iPage, @Param("userId") Long userId, @Param("companyId") Long companyId, @Param("opinion") String opinion);

}