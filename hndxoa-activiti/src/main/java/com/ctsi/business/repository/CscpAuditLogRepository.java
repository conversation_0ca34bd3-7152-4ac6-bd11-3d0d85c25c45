package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.business.domain.CscpAuditLog;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface CscpAuditLogRepository extends BaseMapper<CscpAuditLog> {

    //    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    long countByExample(CscpAuditLogExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int deleteByExample(CscpAuditLogExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int deleteByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int insert(CscpAuditLog record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int insertSelective(CscpAuditLog record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    List<CscpAuditLog> selectByExample(CscpAuditLogExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    CscpAuditLog selectByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int updateByExampleSelective(@Param("record") CscpAuditLog record, @Param("example") CscpAuditLogExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int updateByExample(@Param("record") CscpAuditLog record, @Param("example") CscpAuditLogExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int updateByPrimaryKeySelective(CscpAuditLog record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table cscp_audit_log
//     *
//     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
//     */
//    int updateByPrimaryKey(CscpAuditLog record);
//
//
//    List<CscpAuditLog> getAuditLogList(@Param("PROCESS_INSTANCE_ID") String PROCESS_INSTANCE_ID);
}