package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.business.domain.CscpGroupUser;
import com.ctsi.business.domain.CscpGroupUserExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpGroupUserRepository extends BaseMapper<CscpGroupUser> {
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    long countByExample(CscpGroupUserExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByExample(CscpGroupUserExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int deleteByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insert(CscpGroupUser record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int insertSelective(CscpGroupUser record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    List<CscpGroupUser> selectByExample(CscpGroupUserExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    CscpGroupUser selectByPrimaryKey(String id);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExampleSelective(@Param("record") CscpGroupUser record, @Param("example") CscpGroupUserExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByExample(@Param("record") CscpGroupUser record, @Param("example") CscpGroupUserExample example);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKeySelective(CscpGroupUser record);
//
//    /**
//     * This method was generated by MyBatis Generator.
//     * This method corresponds to the database table CSCP_GROUP_USER
//     *
//     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
//     */
//    int updateByPrimaryKey(CscpGroupUser record);
}