package com.ctsi.business.repository;

import com.ctsi.activiti.core.vo.PageQuery;
import com.ctsi.common.utils.PageData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CscpProcPermissionsRepository {

    void saveProcPermissions(PageData pd);

    List<PageData> queryPageProcPermissions(PageQuery pageQuery);

    List<PageData> checkUserProcessTypePermissions(PageData pd);

    List<PageData> queryPermissionByRoleId(@Param("roleId") String roleId);

    void deletePermissionByRoleId(@Param("roleId") String roleId);
}
