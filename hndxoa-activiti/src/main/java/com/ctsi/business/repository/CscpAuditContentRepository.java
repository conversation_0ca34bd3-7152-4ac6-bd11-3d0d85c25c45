package com.ctsi.business.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.business.domain.CscpAuditContent;
import java.util.List;

import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
*/
@Mapper
public interface CscpAuditContentRepository extends MybatisBaseMapper<CscpAuditContent> {
   /* *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    long countByExample(CscpAuditContentExample example);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int deleteByExample(CscpAuditContentExample example);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int deleteByPrimaryKey(String id);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int insert(CscpAuditContent record);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int insertSelective(CscpAuditContent record);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    List<CscpAuditContent> selectByExample(CscpAuditContentExample example);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    CscpAuditContent selectByPrimaryKey(String id);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int updateByExampleSelective(@Param("record") CscpAuditContent record, @Param("example") CscpAuditContentExample example);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int updateByExample(@Param("record") CscpAuditContent record, @Param("example") CscpAuditContentExample example);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int updateByPrimaryKeySelective(CscpAuditContent record);

    *//**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cscp_audit_content
     *
     * @mbg.generated Wed Dec 25 15:02:49 CST 2019
     *//*
    int updateByPrimaryKey(CscpAuditContent record);

    List<CscpAuditContent> getAuditContentList(@Param("PROCESS_INSTANCE_ID") String PROCESS_INSTANCE_ID);*/

    void deleteByprocessInstanceId(@Param("processInstanceId") String processInstanceId);
}