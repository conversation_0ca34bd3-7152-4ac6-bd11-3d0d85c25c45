package com.ctsi.business.repository;

import com.ctsi.ssdc.admin.domain.CscpUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CscpPersonRepository {
    List<CscpUser> queryUserListByOrgId(@Param("parentId") String parentId);

    List<CscpUser> queryUserListByGroupId(@Param("parentId") String parentId);
}
